# Finantic - ERP Contable para Colombia

Sistema ERP contable diseñado específicamente para Colombia con soporte completo para DIAN, facturación electrónica y normativas fiscales locales.

## Características Principales

- **Fiscal Colombia**: DIAN, NIT, RUT, IVA, ICA, retenciones (ReteFuente, ReteIVA, ReteICA)
- **Facturación Electrónica**: Numeración DIAN, prefijos, resoluciones
- **PUC Colombia**: Mapping al Plan Único de Cuentas Comercial
- **Multimoneda**: COP base, soporte USD/EUR
- **RBAC**: Permisos por roles (Admin, Contabilidad, Compras, Ventas, etc.)
- **Auditoría**: Trazabilidad completa de operaciones

## Módulos

1. **Tablero**: KPIs, gráficos, calendario de vencimientos
2. **Clientes**: Cuentas por cobrar, facturación, cobranzas
3. **Proveedores**: Cuentas por pagar, retenciones, pagos
4. **Tesorería**: Cajas, bancos, cheques, conciliación
5. **Inventario**: Stock, movimientos, valuación FIFO/LIFO/PPP
6. **Contabilidad**: Plan de cuentas, asientos, reportes
7. **Configuración**: Empresa, impuestos, talonarios, centros de costo

## Stack Tecnológico

- **Frontend**: React + TypeScript + Tailwind CSS
- **Backend**: NestJS + TypeScript + PostgreSQL + Prisma
- **Infraestructura**: Docker Compose
- **Testing**: Jest + React Testing Library
- **Linting**: ESLint + Prettier

## Inicio Rápido

### Prerrequisitos
- Docker y Docker Compose
- Node.js 18+ (para desarrollo local)
- PostgreSQL 15+ (para desarrollo local)

### Desarrollo con Docker (Recomendado)

```bash
# Clonar el repositorio
git clone <repository-url>
cd finantic

# Opción 1: Inicio rápido (recomendado)
./quick-start.sh

# Opción 2: Configuración manual
make dev

# La aplicación estará disponible en:
# - Frontend: http://localhost:3003
# - Backend API: http://localhost:3004
# - Documentación API: http://localhost:3004/api/docs
```

### Configuración de Puertos

Si tienes conflictos de puertos, usa una de estas opciones:

```bash
# Verificar puertos disponibles
./quick-start.sh  # Opción 4

# Configuración personalizada de puertos
./configure-ports.sh

# Usar puertos alternativos predefinidos
./quick-start.sh  # Opción 2 (Frontend: 3003, Backend: 3004)
```

### Desarrollo Local

```bash
# Instalar dependencias
make install

# Configurar variables de entorno
cp apps/api/.env.example apps/api/.env

# Iniciar base de datos
docker-compose up db -d

# Generar cliente Prisma y ejecutar migraciones
cd apps/api
npx prisma generate
npx prisma db push
npx prisma db seed

# Iniciar desarrollo local
make dev-local
```

### Comandos Útiles

```bash
# Ejecutar tests
make test

# Linting
make lint

# Build producción
make build

# Ver logs
make logs

# Resetear base de datos
make db-reset

# Limpiar contenedores
make clean
```

## Estructura del Proyecto

```
finantic/
├── apps/
│   ├── web/          # Frontend React
│   └── api/          # Backend NestJS
├── packages/
│   └── shared/       # Tipos compartidos
├── docker-compose.yml
├── Makefile
└── README.md
```

## Configuración Inicial

El sistema incluye datos de prueba:
- 3 clientes con diferentes tipos de responsabilidad fiscal
- 3 proveedores con configuración de retenciones
- 10 items de inventario
- 2 depósitos
- 6 facturas (AR/AP) con diferentes estados
- 2 pagos y 2 cobranzas de ejemplo

## Cumplimiento Normativo

- Facturación electrónica DIAN
- Retenciones automáticas según configuración
- Reportes fiscales exportables
- Plan de cuentas PUC Colombia
- Trazabilidad completa para auditorías