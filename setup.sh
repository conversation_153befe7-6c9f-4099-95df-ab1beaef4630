#!/bin/bash

echo "🚀 Configurando Finantic ERP..."

# Verificar que Docker esté instalado
if ! command -v docker &> /dev/null; then
    echo "❌ Docker no está instalado. Por favor instala Docker primero."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose no está instalado. Por favor instala Docker Compose primero."
    exit 1
fi

# Crear archivo .env si no existe
if [ ! -f apps/api/.env ]; then
    echo "📝 Creando archivo .env..."
    cp apps/api/.env.example apps/api/.env
fi

# Construir e iniciar servicios
echo "🔨 Construyendo servicios..."
docker-compose build

echo "🚀 Iniciando servicios..."
docker-compose up -d

# Esperar a que la base de datos esté lista
echo "⏳ Esperando a que la base de datos esté lista..."
sleep 10

# Ejecutar migraciones y seed
echo "📊 Configurando base de datos..."
docker-compose exec api npx prisma db push
docker-compose exec api npx prisma db seed

echo "✅ ¡Finantic ERP está listo!"
echo ""
echo "🌐 Accede a la aplicación en:"
echo "   Frontend: http://localhost:3003"
echo "   API: http://localhost:3004"
echo "   Documentación: http://localhost:3004/api/docs"
echo ""
echo "👤 Credenciales de prueba:"
echo "   Email: <EMAIL>"
echo "   Password: admin123"
echo ""
echo "📋 Comandos útiles:"
echo "   make logs     - Ver logs"
echo "   make clean    - Limpiar contenedores"
echo "   make test     - Ejecutar tests"