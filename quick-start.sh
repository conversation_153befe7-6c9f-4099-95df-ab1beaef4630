#!/bin/bash

echo "🚀 Finantic ERP - Inicio <PERSON>"
echo "==============================="
echo ""
echo "Selecciona una opción:"
echo "1) Puerto estándar (Frontend: 3002, Backend: 3001)"
echo "2) Puertos alternativos (Frontend: 3003, Backend: 3004)"
echo "3) Configuración personalizada"
echo "4) Solo verificar puertos disponibles"
echo ""

read -p "Opción (1-4): " option

case $option in
    1)
        echo "✅ Usando configuración estándar..."
        ;;
    2)
        echo "🔄 Configurando puertos alternativos..."
        # Actualizar a puertos alternativos
        sed -i.bak 's/"3002:3002"/"3003:3003"/g' docker-compose.yml
        sed -i.bak 's/"3001:3001"/"3004:3004"/g' docker-compose.yml
        sed -i.bak 's/PORT: 3002/PORT: 3003/g' docker-compose.yml
        echo "PORT=3003" > apps/web/.env
        echo "REACT_APP_API_URL=http://localhost:3004" >> apps/web/.env
        rm -f docker-compose.yml.bak
        echo "✅ Puertos configurados: Frontend 3003, Backend 3004"
        ;;
    3)
        echo "🔧 Iniciando configuración personalizada..."
        ./configure-ports.sh
        exit 0
        ;;
    4)
        echo "🔍 Verificando puertos disponibles..."
        for port in 3000 3001 3002 3003 3004 3005; do
            if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
                echo "❌ Puerto $port: OCUPADO"
            else
                echo "✅ Puerto $port: DISPONIBLE"
            fi
        done
        echo ""
        echo "💡 Puertos recomendados disponibles para usar:"
        echo "   Frontend: 3002 o 3003"
        echo "   Backend: 3001 o 3004"
        exit 0
        ;;
    *)
        echo "❌ Opción inválida"
        exit 1
        ;;
esac

echo ""
echo "🔨 Iniciando servicios..."

# Verificar que Docker esté corriendo
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker no está corriendo. Por favor inicia Docker primero."
    exit 1
fi

# Crear .env si no existe
if [ ! -f apps/api/.env ]; then
    cp apps/api/.env.example apps/api/.env
fi

# Iniciar servicios
docker-compose up --build -d

echo "⏳ Esperando a que los servicios estén listos..."
sleep 15

# Verificar que los servicios estén corriendo
if docker-compose ps | grep -q "Up"; then
    echo "✅ ¡Servicios iniciados correctamente!"
    
    # Obtener puertos actuales del docker-compose
    FRONTEND_PORT=$(grep -o '"[0-9]*:' docker-compose.yml | grep -v 5432 | head -1 | tr -d '":')
    BACKEND_PORT=$(grep -o '"[0-9]*:' docker-compose.yml | grep -v 5432 | tail -1 | tr -d '":')
    
    echo ""
    echo "🌐 Accede a la aplicación:"
    echo "   Frontend: http://localhost:$FRONTEND_PORT"
    echo "   Backend: http://localhost:$BACKEND_PORT"
    echo "   Documentación: http://localhost:$BACKEND_PORT/api/docs"
    echo ""
    echo "👤 Credenciales de prueba:"
    echo "   Email: <EMAIL>"
    echo "   Password: admin123"
else
    echo "❌ Error al iniciar los servicios"
    echo "📋 Revisa los logs con: docker-compose logs"
fi