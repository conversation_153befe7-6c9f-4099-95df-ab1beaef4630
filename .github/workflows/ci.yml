name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test-api:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: finantic_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: apps/api/package-lock.json
    
    - name: Install API dependencies
      working-directory: apps/api
      run: npm ci
    
    - name: Generate Prisma client
      working-directory: apps/api
      run: npx prisma generate
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/finantic_test
    
    - name: Run API tests
      working-directory: apps/api
      run: npm test
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/finantic_test
        JWT_SECRET: test-secret
    
    - name: Lint API
      working-directory: apps/api
      run: npm run lint

  test-web:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: apps/web/package-lock.json
    
    - name: Install Web dependencies
      working-directory: apps/web
      run: npm ci
    
    - name: Run Web tests
      working-directory: apps/web
      run: npm test -- --coverage --watchAll=false
    
    - name: Lint Web
      working-directory: apps/web
      run: npm run lint
    
    - name: Build Web
      working-directory: apps/web
      run: npm run build

  build:
    needs: [test-api, test-web]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Build Docker images
      run: |
        docker build -t finantic-api ./apps/api
        docker build -t finantic-web ./apps/web
    
    - name: Test Docker Compose
      run: |
        docker-compose up -d
        sleep 30
        docker-compose ps
        docker-compose down