version: '3.8'

services:
  db:
    image: postgres:15-alpine
    container_name: finantic-db
    environment:
      POSTGRES_DB: finantic
      POSTGRES_USER: finantic
      POSTGRES_PASSWORD: finantic123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - finantic-network

  api:
    image: node:18-alpine
    container_name: finantic-api
    working_dir: /app
    environment:
      DATABASE_URL: *****************************************/finantic
      JWT_SECRET: finantic-jwt-secret-key
      NODE_ENV: development
      PORT: 3001
    ports:
      - "3001:3001"
    depends_on:
      - db
    volumes:
      - ./apps/api:/app
    command: sh -c "apk add --no-cache openssl && npm install && npx prisma generate && sleep 10 && npx prisma db push && npm run start:dev"
    networks:
      - finantic-network

  web:
    build:
      context: ./apps/web
      dockerfile: Dockerfile
    container_name: finantic-web
    environment:
      REACT_APP_API_URL: http://localhost:3001
      PORT: 3002
    ports:
      - "3002:3002"
    depends_on:
      - api
    volumes:
      - ./apps/web:/app
      - /app/node_modules
    networks:
      - finantic-network

volumes:
  postgres_data:

networks:
  finantic-network:
    driver: bridge