# Design Document - <PERSON><PERSON><PERSON><PERSON> (Interacciones Detalladas)

## Overview

Este documento describe el diseño técnico para implementar las interacciones detalladas del módulo de Clientes, incluyendo la arquitectura de componentes, gestión de estado, APIs y patrones de interacción específicos basados en las capturas de pantalla proporcionadas.

## Architecture

### Component Structure

```
CustomersModule/
├── CustomersPage.tsx (Container principal)
├── components/
│   ├── CustomersList.tsx (Panel izquierdo)
│   ├── CustomersDetail.tsx (Panel derecho)
│   ├── CustomerTabs.tsx (Tabs del detalle)
│   ├── OutstandingInvoices.tsx (Facturas adeudadas)
│   ├── CustomerAccount.tsx (Cuenta corriente)
│   ├── GeneralInfo.tsx (Información general)
│   ├── FiscalData.tsx (Datos fiscales)
│   ├── CommercialData.tsx (Datos comerciales)
│   ├── InvoiceModal.tsx (Modal crear factura)
│   ├── ContactModal.tsx (Modal agregar contacto)
│   └── DataTable.tsx (Tabla reutilizable)
├── hooks/
│   ├── useCustomers.ts (Gestión de clientes)
│   ├── useCustomerDetail.ts (Detalle del cliente)
│   ├── useInvoices.ts (Gestión de facturas)
│   └── usePermissions.ts (Validación de permisos)
└── types/
    ├── customer.types.ts
    ├── invoice.types.ts
    └── interaction.types.ts
```

### State Management

#### Global State (Context/Zustand)
```typescript
interface CustomersState {
  // Lista de clientes
  customers: Customer[];
  filteredCustomers: Customer[];
  selectedCustomer: Customer | null;
  
  // Filtros y búsqueda
  activeFilter: 'active' | 'inactive';
  searchTerm: string;
  
  // Paginación
  currentPage: number;
  totalPages: number;
  pageSize: number;
  
  // UI State
  activeTab: string;
  isLoading: boolean;
  
  // Modals
  isInvoiceModalOpen: boolean;
  isContactModalOpen: boolean;
}
```

#### Local Component State
- Formularios: React Hook Form para validación
- Modals: Estado local para apertura/cierre
- Grillas: Estado local para ordenamiento y paginación

## Components and Interfaces

### 1. CustomersPage (Container Principal)

```typescript
interface CustomersPageProps {}

const CustomersPage: React.FC<CustomersPageProps> = () => {
  // Layout de dos paneles fijo
  // Gestión de tabs superiores
  // Coordinación entre lista y detalle
}
```

**Responsabilidades:**
- Layout principal de dos paneles
- Gestión de tabs superiores del módulo
- Coordinación entre componentes hijo
- Gestión de permisos globales

### 2. CustomersList (Panel Izquierdo)

```typescript
interface CustomersListProps {
  customers: Customer[];
  selectedCustomer: Customer | null;
  onCustomerSelect: (customer: Customer) => void;
  onCustomerDoubleClick: (customer: Customer) => void;
}
```

**Interacciones Específicas:**
- Toggle Activos/Inactivos con recarga de lista
- Búsqueda con debounce 300ms
- Clic simple: selección visual únicamente
- Doble clic: selección + carga panel derecho
- Paginación sin perder filtros

### 3. CustomersDetail (Panel Derecho)

```typescript
interface CustomersDetailProps {
  customer: Customer | null;
  activeTab: string;
  onTabChange: (tab: string) => void;
}
```

**Estados:**
- Sin selección: "Seleccionar cliente de la lista"
- Con selección: Tabs del cliente
- Carga: Loading spinner

### 4. OutstandingInvoices (Facturas Adeudadas)

```typescript
interface OutstandingInvoicesProps {
  customerId: string;
  invoices: Invoice[];
  onInvoiceDoubleClick: (invoice: Invoice) => void;
  onAddInvoice: () => void;
  onAddReceipt: () => void;
}
```

**Columnas Exactas (orden fijo):**
1. Nro. Comprob.
2. Tipo
3. Fecha pago
4. Descripción
5. Valor ME
6. Total
7. Cobrado
8. Saldo
9. Saldo acumul.

**Interacciones:**
- Doble clic en factura: abrir detalle
- Ordenamiento por columna
- Paginación independiente

### 5. InvoiceModal (Modal Crear Factura)

```typescript
interface InvoiceModalProps {
  isOpen: boolean;
  customer: Customer;
  onClose: () => void;
  onSave: (invoice: InvoiceData) => void;
}

interface InvoiceData {
  // Encabezado
  fechaFactura: Date;
  fechaVencimiento: Date;
  condicionVenta: 'Contado' | 'Credito';
  tipoComprobante: string;
  tipoFactura: 'A' | 'B' | 'C';
  estado: 'Borrador' | 'Aprobada';
  numeroFactura: string;
  descripcion: string;
  moneda: string;
  listaPrecio: string;
  generarRemito: boolean;
  
  // Items
  items: InvoiceItem[];
  
  // Totales (calculados)
  netoGravado: number;
  netoNoGravado: number;
  totalIVA: number;
  percepcionIVA: number;
  percepcionICA: number;
  totalFactura: number;
  
  // Adjuntos
  attachments: File[];
}
```

**Validaciones:**
- Fecha vencimiento ≥ Fecha factura
- Items: Cant > 0, P Unit ≥ 0, 0 ≤ %Desc ≤ 100
- Cálculos automáticos de IVA y percepciones

### 6. FiscalData (Datos Fiscales)

```typescript
interface FiscalDataProps {
  customer: Customer;
  onSave: (data: FiscalData) => void;
  onDianConsult: (nit: string) => void;
}
```

**Campos en Orden Exacto:**
1. NIT (con botón "Consultar en DIAN")
2. Razón social
3. Nombre
4. Condición IVA/Resp. fiscal
5. Dirección
6. Ciudad
7. Cod. Postal
8. Provincia (select)
9. País (select)
10. Teléfono
11. DNI/Pasaporte
12. Email

**Tabs Secundarios:**
- Contactos
- Comentarios
- Historia
- Facturas recurrentes

## Data Models

### Customer Model (Extendido)

```typescript
interface Customer {
  id: string;
  code: string;
  
  // Datos básicos
  name: string;
  tradeName?: string;
  nit?: string;
  documentType: DocumentType;
  
  // Datos fiscales
  taxResponsibility: TaxResponsibility;
  address?: string;
  city?: string;
  state?: string;
  country: string;
  postalCode?: string;
  phone?: string;
  email?: string;
  
  // Datos comerciales
  active: boolean;
  registrationDate: Date;
  saleCondition: 'Contado' | 'Credito';
  discountPercentage: number;
  ivaPercentage: number;
  incomeAccount?: string;
  priceList?: string;
  transporter?: string;
  
  // Dirección de envío
  shippingAddress?: Address;
  
  // Configuración
  creditLimit: number;
  creditDays: number;
  
  // Metadatos
  createdAt: Date;
  updatedAt: Date;
}

interface Address {
  address: string;
  city: string;
  postalCode: string;
  state: string;
  country: string;
}

interface Contact {
  id: string;
  name: string;
  position: string;
  email: string;
  phone: string;
  notes?: string;
}
```

### Invoice Model (Detallado)

```typescript
interface Invoice {
  id: string;
  number: string;
  customerId: string;
  
  // Encabezado
  date: Date;
  dueDate?: Date;
  saleCondition: 'Contado' | 'Credito';
  documentType: string;
  invoiceType: 'A' | 'B' | 'C';
  status: InvoiceStatus;
  description?: string;
  currency: string;
  priceList?: string;
  generateDelivery: boolean;
  
  // Items
  items: InvoiceItem[];
  
  // Totales
  taxableNet: number;
  nonTaxableNet: number;
  totalIVA: number;
  ivaPerception: number;
  icaPerception: number;
  total: number;
  
  // Estado de pago
  paidAmount: number;
  balance: number;
  
  // Adjuntos
  attachments: Attachment[];
  
  // Metadatos
  createdAt: Date;
  updatedAt: Date;
}

interface InvoiceItem {
  id: string;
  itemCode: string;
  description: string;
  warehouse: string;
  freight: number;
  salespeople: string[];
  unit: string;
  quantity: number;
  unitPrice: number;
  discountPercentage: number;
  ivaPercentage: number;
  subtotal: number;
  account: string;
}
```

## Error Handling

### Validation Patterns

```typescript
// Validación de fechas
const validateDates = (fechaFactura: Date, fechaVencimiento: Date) => {
  if (fechaVencimiento < fechaFactura) {
    throw new ValidationError('Fecha vencimiento debe ser mayor o igual a fecha factura');
  }
};

// Validación de items
const validateInvoiceItem = (item: InvoiceItem) => {
  if (item.quantity <= 0) {
    throw new ValidationError('Cantidad debe ser mayor a 0');
  }
  if (item.unitPrice < 0) {
    throw new ValidationError('Precio unitario debe ser mayor o igual a 0');
  }
  if (item.discountPercentage < 0 || item.discountPercentage > 100) {
    throw new ValidationError('Descuento debe estar entre 0 y 100%');
  }
};
```

### Error Display
- Mensajes de error específicos por campo
- Toast notifications para operaciones exitosas
- Loading states durante operaciones asíncronas

## Testing Strategy

### Unit Tests
- Componentes individuales con React Testing Library
- Hooks personalizados con renderHook
- Funciones de validación y cálculo
- Transformadores de datos

### Integration Tests
- Flujos completos de interacción
- Navegación entre tabs
- Operaciones CRUD de clientes e invoices
- Validación de permisos

### E2E Tests
- Flujo completo: buscar cliente → seleccionar → crear factura
- Validación de datos fiscales con DIAN
- Gestión de adjuntos
- Responsive design en diferentes dispositivos

### Test Scenarios Específicos

```typescript
describe('Customer Interactions', () => {
  test('single click selects customer without loading detail', () => {
    // Verificar que clic simple solo resalta fila
  });
  
  test('double click loads customer detail with outstanding invoices tab', () => {
    // Verificar carga completa del panel derecho
  });
  
  test('search filters customers with 300ms debounce', () => {
    // Verificar debounce y filtrado
  });
  
  test('invoice modal calculates totals automatically', () => {
    // Verificar cálculos automáticos
  });
  
  test('permissions disable buttons correctly', () => {
    // Verificar permisos por rol
  });
});
```

## Performance Considerations

### Optimization Strategies
- Virtualización de listas largas de clientes
- Lazy loading de tabs no activos
- Debounce en búsquedas (300ms)
- Memoización de cálculos complejos
- Paginación server-side para grandes datasets

### Caching Strategy
- Cache de lista de clientes con invalidación
- Cache de datos de cliente seleccionado
- Cache de opciones de selects (provincias, países, etc.)

### Bundle Optimization
- Code splitting por tabs
- Lazy loading de modals
- Tree shaking de librerías no utilizadas