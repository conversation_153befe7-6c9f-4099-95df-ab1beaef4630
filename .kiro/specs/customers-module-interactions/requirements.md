# Requirements Document - <PERSON><PERSON><PERSON><PERSON> (Interacciones Detalladas)

## Introduction

Este documento especifica las interacciones detalladas del módulo de Clientes de Finantic ERP, basado en las capturas de pantalla proporcionadas y los flujos de trabajo específicos requeridos. El módulo debe implementar un layout de dos paneles (lista izquierda + detalle derecho) con navegación por tabs y funcionalidades completas de gestión de clientes, facturas y cobranzas.

## Requirements

### Requirement 1: Layout y Navegación Principal

**User Story:** Como usuario del sistema, quiero navegar por el módulo de clientes con un layout consistente de dos paneles, para poder gestionar eficientemente la información de clientes.

#### Acceptance Criteria

1. WHEN accedo a /clientes THEN el sistema SHALL mostrar un layout fijo con panel izquierdo (lista) y panel derecho (detalle)
2. WHEN cargo la página THEN el sistema SHALL mostrar tabs superiores: "Resumen clientes", "Gestión de clientes", "Importación de facturas", "Mercado Pago"
3. WHEN cambio entre tabs superiores THEN el sistema SHALL mantener el layout de dos paneles sin modificar la estructura
4. WHEN no hay cliente seleccionado THEN el panel derecho SHALL mostrar "Seleccionar cliente de la lista"
5. WHEN hay datos en cualquier panel THEN el sistema SHALL mostrar paginador inferior con flechas, input de página, icono refrescar y texto "Página X de Y"
6. WHEN no hay datos THEN el sistema SHALL mostrar "Sin datos para mostrar"

### Requirement 2: Lista de Clientes y Filtros

**User Story:** Como usuario, quiero filtrar y buscar clientes en la lista izquierda, para encontrar rápidamente el cliente que necesito gestionar.

#### Acceptance Criteria

1. WHEN cargo la lista THEN el sistema SHALL mostrar toggle "Activos/Inactivos" arriba del listado
2. WHEN hago clic en "Activos" o "Inactivos" THEN el sistema SHALL recargar la lista filtrando por ese estado
3. WHEN escribo en "Buscar cliente:" THEN el sistema SHALL filtrar por Nombre y Razón social con debounce de 300ms
4. WHEN hago clic simple en una fila THEN el sistema SHALL seleccionar la fila (resaltarla) sin cambiar el panel derecho
5. WHEN hago doble clic en una fila THEN el sistema SHALL seleccionar la fila Y cargar el panel derecho con las pestañas del cliente
6. WHEN uso el paginador THEN el sistema SHALL cambiar página sin perder filtros aplicados
7. WHEN la lista está vacía THEN el sistema SHALL mostrar mensaje apropiado

### Requirement 3: Panel de Detalle - Facturas Adeudadas

**User Story:** Como usuario, quiero ver las facturas adeudadas de un cliente seleccionado, para gestionar las cuentas por cobrar.

#### Acceptance Criteria

1. WHEN selecciono un cliente con doble clic THEN el sistema SHALL mostrar el tab "Facturas adeudadas" como activo por defecto
2. WHEN el tab está activo THEN el sistema SHALL mostrar grilla con columnas en orden exacto: "Nro. Comprob.", "Tipo", "Fecha pago", "Descripción", "Valor ME", "Total", "Cobrado", "Saldo", "Saldo acumul."
3. WHEN hago doble clic en una factura THEN el sistema SHALL abrir el detalle de la factura en modal de lectura/edición
4. WHEN tengo permisos THEN el sistema SHALL mostrar botones "Agregar factura", "Agregar cobranza", "Nuevo cliente", "Importar"
5. WHEN hago clic en encabezado de columna THEN el sistema SHALL ordenar asc/desc por esa columna
6. WHEN uso paginador del panel derecho THEN el sistema SHALL paginar sin perder selección del cliente
7. WHEN no hay facturas THEN el sistema SHALL mostrar "Sin datos para mostrar"

### Requirement 4: Modal Ingresar Factura

**User Story:** Como usuario con permisos de ventas, quiero crear facturas de venta con todos los campos requeridos, para registrar las transacciones comerciales.

#### Acceptance Criteria

1. WHEN hago clic en "Agregar factura" THEN el sistema SHALL abrir modal "Ingresar factura para [CLIENTE]"
2. WHEN el modal se abre THEN el sistema SHALL mostrar encabezado con campos en orden: Fecha factura, Fecha vencimiento, Condición de venta, Tipo comprobante, Tipo factura, Estado, Nro. factura, Descripción, Moneda, Lista de precios
3. WHEN el modal se abre THEN el sistema SHALL mostrar checkbox "Generar remito automático" y enlace "Datos adicionales del comprobante"
4. WHEN el modal se abre THEN el sistema SHALL mostrar grilla de ítems con columnas: Código, Descripción, Depósito, FLETE, Vendedores, UM, Cant, P Unit, %Desc, %IVA, Subtotal, Cuenta
5. WHEN hago clic en "Agregar 5 líneas" THEN el sistema SHALL insertar 5 filas vacías en la grilla
6. WHEN el modal se abre THEN el sistema SHALL mostrar panel de totales con: Neto gravado, Neto no gravado, Total IVA, Percepción IVA, Percepción ICA, Total factura
7. WHEN el modal se abre THEN el sistema SHALL mostrar sección de adjuntos con botón "Adjuntar archivo"
8. WHEN valido campos THEN el sistema SHALL verificar: Fecha vencimiento ≥ Fecha factura, Cant > 0, P Unit ≥ 0, 0 ≤ %Desc ≤ 100
9. WHEN modifico ítems THEN el sistema SHALL calcular automáticamente IVA, percepciones y total
10. WHEN hago clic en "Guardar" THEN el sistema SHALL cerrar modal y refrescar grilla de facturas adeudadas

### Requirement 5: Información General - Datos Fiscales

**User Story:** Como usuario, quiero editar los datos fiscales de un cliente, para mantener actualizada la información tributaria.

#### Acceptance Criteria

1. WHEN hago clic en tab "Información general" THEN el sistema SHALL mostrar formulario de datos fiscales
2. WHEN el formulario se carga THEN el sistema SHALL mostrar campos en orden: NIT, Razón social, Nombre, Condición IVA/Resp. fiscal, Dirección, Ciudad, Cod. Postal, Provincia, País, Teléfono, DNI/Pasaporte, Email
3. WHEN hago clic en "Consultar en DIAN" THEN el sistema SHALL validar/completar datos desde API DIAN (o mostrar stub)
4. WHEN el formulario se carga THEN el sistema SHALL mostrar tabs secundarios: Contactos, Comentarios, Historia, Facturas recurrentes
5. WHEN hago clic en "Agregar contacto" THEN el sistema SHALL abrir modal con campos: Nombre, Cargo, Email, Teléfono, Notas
6. WHEN hago clic en "Guardar" THEN el sistema SHALL persistir cambios y mostrar confirmación
7. WHEN hay errores de validación THEN el sistema SHALL mostrar mensajes de error específicos

### Requirement 6: Información General - Datos Comerciales

**User Story:** Como usuario, quiero gestionar los datos comerciales y de envío de un cliente, para configurar correctamente las condiciones de venta.

#### Acceptance Criteria

1. WHEN accedo a datos comerciales THEN el sistema SHALL mostrar controles en orden: Activo, Fecha alta, Dirección fiscal, Ciudad, Cod. Postal, Provincia, País, Condición de venta, % Desc, % IVA, Cuenta de ingresos, Lista de precios, Transportista
2. WHEN el formulario se carga THEN el sistema SHALL mostrar bloque "Dirección para envíos" con campos separados
3. WHEN hago clic en "Guardar" THEN el sistema SHALL validar campos obligatorios y persistir cambios
4. WHEN hay campos inválidos THEN el sistema SHALL mostrar errores de validación específicos

### Requirement 7: Cuenta Cliente (Cuenta Corriente)

**User Story:** Como usuario, quiero ver los movimientos de cuenta corriente de un cliente, para analizar el historial de transacciones.

#### Acceptance Criteria

1. WHEN hago clic en tab "Cuenta cliente" THEN el sistema SHALL mostrar grilla de movimientos
2. WHEN la grilla se carga THEN el sistema SHALL mostrar columnas: Fecha, Comprobante, Tipo Comp, Descripción, Valor ME, Débito, Crédito, Saldo adeud.
3. WHEN hago doble clic en una fila THEN el sistema SHALL abrir el documento origen (factura, nota de crédito, recibo)
4. WHEN uso el paginador THEN el sistema SHALL mantener el comportamiento consistente con otras grillas
5. WHEN no hay movimientos THEN el sistema SHALL mostrar "Sin datos para mostrar"

### Requirement 8: Gestión de Permisos y Estados

**User Story:** Como administrador del sistema, quiero que los permisos por rol se respeten en todas las funcionalidades del módulo de clientes.

#### Acceptance Criteria

1. WHEN el usuario tiene rol Ventas/Contabilidad THEN el sistema SHALL habilitar botón "Agregar factura"
2. WHEN el usuario tiene rol Tesorería/Ventas THEN el sistema SHALL habilitar botón "Agregar cobranza"
3. WHEN el usuario tiene rol Consulta THEN el sistema SHALL mostrar datos en modo solo lectura
4. WHEN el usuario no tiene permisos THEN el sistema SHALL deshabilitar botones con estilo disabled
5. WHEN se realizan operaciones THEN el sistema SHALL validar permisos en backend

### Requirement 9: Consistencia Visual y UX

**User Story:** Como usuario, quiero que la interfaz sea consistente con el diseño establecido, para tener una experiencia de usuario coherente.

#### Acceptance Criteria

1. WHEN uso cualquier funcionalidad THEN la UI SHALL verse idéntica a las capturas: textos, orden, tamaños, colores
2. WHEN interactúo con elementos THEN el sistema SHALL mantener encabezados sticky en grillas
3. WHEN uso paginaciones y filtros THEN el sistema SHALL mantener la selección del cliente
4. WHEN creo/edito datos THEN el panel derecho SHALL actualizarse sin recargar toda la página
5. WHEN hay tooltips "?" THEN el sistema SHALL mostrar ayuda breve al pasar mouse o hacer clic
6. WHEN hay mensajes de estado THEN el sistema SHALL mostrarlos en el mismo lugar y estilo de las capturas