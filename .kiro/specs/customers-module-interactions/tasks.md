# Implementation Plan - <PERSON><PERSON><PERSON>lo <PERSON> (Interacciones Detalladas)

## Task Overview

Este plan implementa las interacciones específicas del módulo de Clientes basado en las capturas de pantalla y especificaciones detalladas. Cada tarea se enfoca en implementar comportamientos exactos de UI/UX según las imágenes proporcionadas.

## Implementation Tasks

- [ ] 1. Configurar estructura base del módulo de clientes
  - Crear estructura de carpetas y archivos base
  - Configurar routing para /clientes
  - Implementar layout de dos paneles fijo
  - _Requirements: 1.1, 1.2, 1.3_

- [ ] 2. Implementar componente CustomersList (Panel Izquierdo)
  - [ ] 2.1 Crear componente base de lista de clientes
    - Implementar estructura de tabla con columnas: Nombre, Razón social, Saldo
    - Configurar estilos según capturas de pantalla
    - Implementar paginación inferior con controles específicos
    - _Requirements: 2.1, 2.7_

  - [ ] 2.2 Implementar filtros y búsqueda
    - Crear toggle Activos/Inactivos con recarga de lista
    - Implementar campo "Buscar cliente:" con debounce 300ms
    - Filtrar por Nombre y Razón social simultáneamente
    - _Requirements: 2.1, 2.2, 2.3_

  - [ ] 2.3 Implementar interacciones de selección específicas
    - Clic simple: seleccionar fila (resaltar) sin cambiar panel derecho
    - Doble clic: seleccionar fila Y cargar panel derecho con tabs
    - Mantener estado de selección durante paginación
    - _Requirements: 2.4, 2.5, 2.6_

- [ ] 3. Implementar componente CustomersDetail (Panel Derecho)
  - [ ] 3.1 Crear estructura base del panel de detalle
    - Implementar estado "Seleccionar cliente de la lista" por defecto
    - Crear sistema de tabs: Facturas adeudadas, Cuenta cliente, Información general, Otra información
    - Configurar navegación entre tabs sin perder selección de cliente
    - _Requirements: 1.4, 3.1_

  - [ ] 3.2 Implementar tab "Facturas adeudadas" como default
    - Crear grilla con columnas exactas: Nro. Comprob., Tipo, Fecha pago, Descripción, Valor ME, Total, Cobrado, Saldo, Saldo acumul.
    - Implementar doble clic en factura para abrir detalle
    - Agregar botones de acción según permisos: Agregar factura, Agregar cobranza
    - _Requirements: 3.1, 3.2, 3.3, 3.4_

  - [ ] 3.3 Implementar ordenamiento y paginación de grilla
    - Clic en encabezado de columna para ordenar asc/desc
    - Paginación independiente del panel derecho
    - Mantener selección de cliente durante paginación
    - _Requirements: 3.5, 3.6_

- [ ] 4. Implementar modal "Ingresar factura"
  - [ ] 4.1 Crear estructura base del modal
    - Implementar modal con título "Ingresar factura para [CLIENTE]"
    - Crear layout según captura: encabezado, ítems, totales, adjuntos, botones
    - Configurar apertura desde botón "Agregar factura"
    - _Requirements: 4.1, 4.2_

  - [ ] 4.2 Implementar sección de encabezado de factura
    - Campos en orden exacto: Fecha factura, Fecha vencimiento, Condición de venta, Tipo comprobante, Tipo factura, Estado
    - Agregar: Nro. factura, Descripción, Moneda, Lista de precios
    - Implementar checkbox "Generar remito automático"
    - Agregar enlace "Datos adicionales del comprobante"
    - _Requirements: 4.2, 4.3_

  - [ ] 4.3 Implementar grilla de ítems de factura
    - Columnas exactas: Código, Descripción, Depósito, FLETE, Vendedores, UM, Cant, P Unit, %Desc, %IVA, Subtotal, Cuenta
    - Botón "Agregar 5 líneas" para insertar filas vacías
    - Botón "Remitos no facturados de este cliente"
    - _Requirements: 4.4, 4.5_

  - [ ] 4.4 Implementar panel de totales automáticos
    - Campos calculados: Neto gravado, Neto no gravado, Total IVA, Percepción IVA, Percepción ICA, Total factura
    - Cálculo automático al modificar ítems
    - Link "Total IVA" para breakdown por alícuota
    - _Requirements: 4.6, 4.9_

  - [ ] 4.5 Implementar validaciones específicas del modal
    - Validar: Fecha vencimiento ≥ Fecha factura
    - Validar ítems: Cant > 0, P Unit ≥ 0, 0 ≤ %Desc ≤ 100
    - Mostrar errores de validación en tiempo real
    - _Requirements: 4.8_

  - [ ] 4.6 Implementar sección de adjuntos y botones finales
    - Sección "Adjuntos" con botón "Adjuntar archivo"
    - Botones: "Guardar", "Guardar e imprimir", "Cancelar"
    - Acción guardar: cerrar modal y refrescar grilla
    - _Requirements: 4.7, 4.10_

- [ ] 5. Implementar tab "Información general" - Datos fiscales
  - [ ] 5.1 Crear formulario de datos fiscales
    - Campos en orden exacto: NIT, Razón social, Nombre, Condición IVA/Resp. fiscal
    - Continuar con: Dirección, Ciudad, Cod. Postal, Provincia, País, Teléfono, DNI/Pasaporte, Email
    - Botón "Consultar en DIAN" junto al campo NIT
    - _Requirements: 5.1, 5.2_

  - [ ] 5.2 Implementar consulta DIAN/AFIP
    - Acción del botón para completar/validar datos automáticamente
    - Integración con API DIAN (o stub si no disponible)
    - Actualización automática de campos relacionados
    - _Requirements: 5.2_

  - [ ] 5.3 Implementar tabs secundarios de información
    - Tabs: Contactos, Comentarios, Historia, Facturas recurrentes
    - Modal "Agregar contacto" con campos: Nombre, Cargo, Email, Teléfono, Notas
    - Botón "Guardar" con persistencia y confirmación
    - _Requirements: 5.3, 5.4, 5.5_

- [ ] 6. Implementar datos comerciales y envío
  - [ ] 6.1 Crear formulario de datos comerciales
    - Controles en orden: Activo, Fecha alta, Dirección fiscal, Ciudad, Cod. Postal, Provincia, País
    - Continuar: Condición de venta, % Desc, % IVA, Cuenta de ingresos, Lista de precios, Transportista
    - Bloque separado "Dirección para envíos"
    - _Requirements: 6.1, 6.2_

  - [ ] 6.2 Implementar validaciones y guardado
    - Validar campos obligatorios antes de guardar
    - Mostrar errores específicos por campo
    - Persistir cambios con confirmación visual
    - _Requirements: 6.3, 6.4_

- [ ] 7. Implementar tab "Cuenta cliente" (Cuenta corriente)
  - [ ] 7.1 Crear grilla de movimientos de cuenta corriente
    - Columnas exactas: Fecha, Comprobante, Tipo Comp, Descripción, Valor ME, Débito, Crédito, Saldo adeud.
    - Doble clic en fila para abrir documento origen
    - Paginación consistente con otras grillas
    - _Requirements: 7.1, 7.2, 7.3_

  - [ ] 7.2 Implementar navegación y estados vacíos
    - Comportamiento de paginador igual a "Facturas adeudadas"
    - Mensaje "Sin datos para mostrar" cuando corresponda
    - _Requirements: 7.4, 7.5_

- [ ] 8. Implementar sistema de permisos por rol
  - [ ] 8.1 Crear hook usePermissions para validación
    - Validar permisos Ventas/Contabilidad para "Agregar factura"
    - Validar permisos Tesorería/Ventas para "Agregar cobranza"
    - Modo solo lectura para rol Consulta
    - _Requirements: 8.1, 8.2, 8.3_

  - [ ] 8.2 Implementar estados disabled de botones
    - Deshabilitar botones sin permisos con estilo disabled
    - Validar permisos en backend para operaciones
    - _Requirements: 8.4, 8.5_

- [ ] 9. Implementar consistencia visual y UX
  - [ ] 9.1 Aplicar estilos exactos según capturas
    - Replicar colores, tamaños, espaciados de las imágenes
    - Implementar encabezados sticky en grillas
    - Mantener textos exactos incluyendo acentos y mayúsculas
    - _Requirements: 9.1, 9.2, 9.5_

  - [ ] 9.2 Implementar tooltips y mensajes de ayuda
    - Tooltips "?" con ayuda breve al pasar mouse o clic
    - Mensajes de estado en ubicaciones exactas de las capturas
    - _Requirements: 9.6_

  - [ ] 9.3 Optimizar actualizaciones sin recarga completa
    - Actualizar panel derecho sin recargar página completa
    - Mantener selecciones durante paginaciones y filtros
    - Refrescar datos después de operaciones CRUD
    - _Requirements: 9.3, 9.4_

- [ ] 10. Implementar APIs backend específicas
  - [ ] 10.1 Crear endpoints para interacciones de clientes
    - GET /api/customers con filtros active/inactive y búsqueda
    - GET /api/customers/:id/outstanding-invoices con paginación
    - GET /api/customers/:id/account-movements con paginación
    - _Requirements: 2.1, 3.1, 7.1_

  - [ ] 10.2 Crear endpoints para gestión de facturas
    - POST /api/ar/invoices con validaciones específicas
    - GET /api/customers/:id/contacts para tabs secundarios
    - POST /api/customers/:id/contacts para agregar contactos
    - _Requirements: 4.1, 5.3_

  - [ ] 10.3 Implementar endpoint de consulta DIAN
    - POST /api/customers/dian-lookup con validación de NIT
    - Retornar datos fiscales estructurados
    - Manejar errores de API externa
    - _Requirements: 5.2_

- [ ] 11. Crear tests específicos para interacciones
  - [ ] 11.1 Tests unitarios de componentes
    - Test de clic simple vs doble clic en lista
    - Test de debounce en búsqueda (300ms)
    - Test de cálculos automáticos en modal factura
    - _Requirements: Todos los requirements de interacción_

  - [ ] 11.2 Tests de integración de flujos completos
    - Flujo: buscar cliente → seleccionar → crear factura → guardar
    - Flujo: navegar tabs → editar datos → guardar cambios
    - Test de permisos por rol en diferentes acciones
    - _Requirements: Flujos completos de usuario_

- [ ] 12. Optimización y performance
  - [ ] 12.1 Implementar optimizaciones específicas
    - Virtualización para listas largas de clientes
    - Lazy loading de tabs no activos
    - Memoización de cálculos de totales en facturas
    - _Requirements: Performance general_

  - [ ] 12.2 Implementar caching estratégico
    - Cache de lista de clientes con invalidación
    - Cache de datos de cliente seleccionado
    - Cache de opciones de selects (provincias, listas de precios)
    - _Requirements: Performance y UX_