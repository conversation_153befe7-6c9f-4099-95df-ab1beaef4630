#!/bin/bash

echo "🔧 Configurador de Puertos - Finantic ERP"
echo "=========================================="

# Función para validar puerto
validate_port() {
    if [[ $1 =~ ^[0-9]+$ ]] && [ $1 -ge 1024 ] && [ $1 -le 65535 ]; then
        return 0
    else
        return 1
    fi
}

# Obtener puerto del frontend
while true; do
    read -p "Puerto para el frontend (actual: 3002): " FRONTEND_PORT
    FRONTEND_PORT=${FRONTEND_PORT:-3002}
    
    if validate_port $FRONTEND_PORT; then
        break
    else
        echo "❌ Puerto inválido. Debe ser un número entre 1024 y 65535."
    fi
done

# Obtener puerto del backend
while true; do
    read -p "Puerto para el backend (actual: 3001): " BACKEND_PORT
    BACKEND_PORT=${BACKEND_PORT:-3001}
    
    if validate_port $BACKEND_PORT; then
        break
    else
        echo "❌ Puerto inválido. Debe ser un número entre 1024 y 65535."
    fi
done

echo ""
echo "📝 Configurando puertos..."

# Actualizar docker-compose.yml
sed -i.bak "s/\"3002:3002\"/\"$FRONTEND_PORT:$FRONTEND_PORT\"/g" docker-compose.yml
sed -i.bak "s/\"3001:3001\"/\"$BACKEND_PORT:$BACKEND_PORT\"/g" docker-compose.yml
sed -i.bak "s/PORT: 3002/PORT: $FRONTEND_PORT/g" docker-compose.yml

# Actualizar nginx.conf
sed -i.bak "s/listen 3002;/listen $FRONTEND_PORT;/g" apps/web/nginx.conf

# Actualizar Dockerfile del frontend
sed -i.bak "s/EXPOSE 3002/EXPOSE $FRONTEND_PORT/g" apps/web/Dockerfile

# Actualizar Dockerfile del backend
sed -i.bak "s/EXPOSE 3001/EXPOSE $BACKEND_PORT/g" apps/api/Dockerfile

# Actualizar package.json del frontend
sed -i.bak "s/PORT=3002/PORT=$FRONTEND_PORT/g" apps/web/package.json

# Actualizar .env del frontend
echo "PORT=$FRONTEND_PORT" > apps/web/.env
echo "REACT_APP_API_URL=http://localhost:$BACKEND_PORT" >> apps/web/.env

# Actualizar .env.example del backend
sed -i.bak "s/PORT=3001/PORT=$BACKEND_PORT/g" apps/api/.env.example

# Actualizar setup.sh
sed -i.bak "s/localhost:3002/localhost:$FRONTEND_PORT/g" setup.sh
sed -i.bak "s/localhost:3001/localhost:$BACKEND_PORT/g" setup.sh

# Actualizar README.md
sed -i.bak "s/localhost:3002/localhost:$FRONTEND_PORT/g" README.md
sed -i.bak "s/localhost:3001/localhost:$BACKEND_PORT/g" README.md

# Limpiar archivos de respaldo
rm -f docker-compose.yml.bak
rm -f apps/web/nginx.conf.bak
rm -f apps/web/Dockerfile.bak
rm -f apps/api/Dockerfile.bak
rm -f apps/web/package.json.bak
rm -f apps/api/.env.example.bak
rm -f setup.sh.bak
rm -f README.md.bak

echo "✅ Configuración completada!"
echo ""
echo "🌐 Nuevos puertos configurados:"
echo "   Frontend: http://localhost:$FRONTEND_PORT"
echo "   Backend: http://localhost:$BACKEND_PORT"
echo "   Documentación: http://localhost:$BACKEND_PORT/api/docs"
echo ""
echo "🚀 Para iniciar la aplicación ejecuta:"
echo "   ./setup.sh"
echo "   o"
echo "   make dev"