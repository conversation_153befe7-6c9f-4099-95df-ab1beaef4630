import { useState, useEffect } from 'react';

export enum UserRole {
  ADMIN = 'ADMIN',
  CONTABILIDAD = 'CONTABILIDAD',
  COMPRAS = 'COMPRAS',
  VENTAS = 'VENTAS',
  TESORERIA = 'TESORERIA',
  CONSULTA = 'CONSULTA'
}

interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  active: boolean;
}

interface Permissions {
  canAddInvoice: boolean;
  canAddReceipt: boolean;
  canEditCustomer: boolean;
  canDeleteCustomer: boolean;
  canViewReports: boolean;
  isReadOnly: boolean;
}

export const usePermissions = () => {
  const [user, setUser] = useState<User | null>(null);
  const [permissions, setPermissions] = useState<Permissions>({
    canAddInvoice: false,
    canAddReceipt: false,
    canEditCustomer: false,
    canDeleteCustomer: false,
    canViewReports: false,
    isReadOnly: true,
  });

  useEffect(() => {
    // Obtener usuario actual del localStorage o API
    const currentUser = getCurrentUser();
    setUser(currentUser);
    
    if (currentUser) {
      const userPermissions = calculatePermissions(currentUser.role);
      setPermissions(userPermissions);
    }
  }, []);

  const getCurrentUser = (): User | null => {
    try {
      const userData = localStorage.getItem('user');
      if (userData) {
        return JSON.parse(userData);
      }
      
      // Usuario por defecto para desarrollo
      return {
        id: '1',
        email: '<EMAIL>',
        name: 'Administrador',
        role: UserRole.ADMIN,
        active: true,
      };
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  };

  const calculatePermissions = (role: UserRole): Permissions => {
    switch (role) {
      case UserRole.ADMIN:
        return {
          canAddInvoice: true,
          canAddReceipt: true,
          canEditCustomer: true,
          canDeleteCustomer: true,
          canViewReports: true,
          isReadOnly: false,
        };

      case UserRole.VENTAS:
        return {
          canAddInvoice: true,
          canAddReceipt: true,
          canEditCustomer: true,
          canDeleteCustomer: false,
          canViewReports: true,
          isReadOnly: false,
        };

      case UserRole.CONTABILIDAD:
        return {
          canAddInvoice: true,
          canAddReceipt: false,
          canEditCustomer: true,
          canDeleteCustomer: false,
          canViewReports: true,
          isReadOnly: false,
        };

      case UserRole.TESORERIA:
        return {
          canAddInvoice: false,
          canAddReceipt: true,
          canEditCustomer: false,
          canDeleteCustomer: false,
          canViewReports: true,
          isReadOnly: false,
        };

      case UserRole.CONSULTA:
        return {
          canAddInvoice: false,
          canAddReceipt: false,
          canEditCustomer: false,
          canDeleteCustomer: false,
          canViewReports: true,
          isReadOnly: true,
        };

      default:
        return {
          canAddInvoice: false,
          canAddReceipt: false,
          canEditCustomer: false,
          canDeleteCustomer: false,
          canViewReports: false,
          isReadOnly: true,
        };
    }
  };

  const hasPermission = (permission: keyof Permissions): boolean => {
    return permissions[permission];
  };

  const hasRole = (role: UserRole): boolean => {
    return user?.role === role;
  };

  const hasAnyRole = (roles: UserRole[]): boolean => {
    return user ? roles.includes(user.role) : false;
  };

  return {
    user,
    permissions,
    hasPermission,
    hasRole,
    hasAnyRole,
  };
};