import { useState, useEffect, useCallback } from 'react';
import { Customer, CustomersState, PaginationInfo } from '../types/customer.types';
import { api } from '../services/api';

const INITIAL_STATE: CustomersState = {
  customers: [],
  filteredCustomers: [],
  selectedCustomer: null,
  activeFilter: 'active',
  searchTerm: '',
  currentPage: 1,
  totalPages: 1,
  pageSize: 20,
  activeTab: 'outstanding-invoices',
  isLoading: false,
  isInvoiceModalOpen: false,
  isContactModalOpen: false,
};

export const useCustomers = () => {
  const [state, setState] = useState<CustomersState>(INITIAL_STATE);

  // Cargar clientes con filtros
  const loadCustomers = useCallback(async (
    search?: string,
    active?: boolean,
    page: number = 1
  ) => {
    setState(prev => ({ ...prev, isLoading: true }));
    
    try {
      const params = new URLSearchParams();
      if (search) params.append('search', search);
      if (active !== undefined) params.append('active', active.toString());
      params.append('page', page.toString());
      params.append('limit', state.pageSize.toString());

      const response = await api.get(`/customers?${params.toString()}`);
      const { data, pagination } = response.data;

      setState(prev => ({
        ...prev,
        customers: data,
        filteredCustomers: data,
        currentPage: pagination.currentPage,
        totalPages: pagination.totalPages,
        isLoading: false,
      }));
    } catch (error) {
      console.error('Error loading customers:', error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [state.pageSize]);

  // Filtrar clientes por estado activo/inactivo
  const setActiveFilter = useCallback((filter: 'active' | 'inactive') => {
    setState(prev => ({ ...prev, activeFilter: filter, currentPage: 1 }));
    loadCustomers(state.searchTerm, filter === 'active', 1);
  }, [loadCustomers, state.searchTerm]);

  // Buscar clientes con debounce
  const setSearchTerm = useCallback((term: string) => {
    setState(prev => ({ ...prev, searchTerm: term, currentPage: 1 }));
    
    // Debounce de 300ms
    const timeoutId = setTimeout(() => {
      loadCustomers(term, state.activeFilter === 'active', 1);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [loadCustomers, state.activeFilter]);

  // Seleccionar cliente (clic simple)
  const selectCustomer = useCallback((customer: Customer) => {
    setState(prev => ({ ...prev, selectedCustomer: customer }));
  }, []);

  // Cargar detalle del cliente (doble clic)
  const loadCustomerDetail = useCallback(async (customer: Customer) => {
    setState(prev => ({ 
      ...prev, 
      selectedCustomer: customer,
      activeTab: 'outstanding-invoices' // Tab por defecto
    }));
  }, []);

  // Cambiar tab activo
  const setActiveTab = useCallback((tab: string) => {
    setState(prev => ({ ...prev, activeTab: tab }));
  }, []);

  // Cambiar página
  const setCurrentPage = useCallback((page: number) => {
    setState(prev => ({ ...prev, currentPage: page }));
    loadCustomers(state.searchTerm, state.activeFilter === 'active', page);
  }, [loadCustomers, state.searchTerm, state.activeFilter]);

  // Modals
  const openInvoiceModal = useCallback(() => {
    setState(prev => ({ ...prev, isInvoiceModalOpen: true }));
  }, []);

  const closeInvoiceModal = useCallback(() => {
    setState(prev => ({ ...prev, isInvoiceModalOpen: false }));
  }, []);

  const openContactModal = useCallback(() => {
    setState(prev => ({ ...prev, isContactModalOpen: true }));
  }, []);

  const closeContactModal = useCallback(() => {
    setState(prev => ({ ...prev, isContactModalOpen: false }));
  }, []);

  // Cargar datos iniciales
  useEffect(() => {
    loadCustomers('', true, 1);
  }, []);

  return {
    ...state,
    loadCustomers,
    setActiveFilter,
    setSearchTerm,
    selectCustomer,
    loadCustomerDetail,
    setActiveTab,
    setCurrentPage,
    openInvoiceModal,
    closeInvoiceModal,
    openContactModal,
    closeContactModal,
  };
};