import { useState, useEffect, useCallback } from 'react';
import { OutstandingInvoice, CustomerMovement, CustomerDetail } from '../types/customer.types';
import { api } from '../services/api';

interface CustomerDetailState {
  outstandingInvoices: OutstandingInvoice[];
  customerMovements: CustomerMovement[];
  customerDetail: CustomerDetail | null;
  isLoading: boolean;
  currentPage: number;
  totalPages: number;
  pageSize: number;
}

const INITIAL_STATE: CustomerDetailState = {
  outstandingInvoices: [],
  customerMovements: [],
  customerDetail: null,
  isLoading: false,
  currentPage: 1,
  totalPages: 1,
  pageSize: 10,
};

export const useCustomerDetail = (customerId?: string) => {
  const [state, setState] = useState<CustomerDetailState>(INITIAL_STATE);

  // Cargar facturas adeudadas
  const loadOutstandingInvoices = useCallback(async (page: number = 1) => {
    if (!customerId) return;

    setState(prev => ({ ...prev, isLoading: true }));
    
    try {
      const params = new URLSearchParams();
      params.append('status', 'APPROVED,PARTIAL');
      params.append('page', page.toString());
      params.append('limit', state.pageSize.toString());

      const response = await api.get(`/customers/${customerId}/invoices?${params.toString()}`);
      const { data, pagination } = response.data;

      // Transformar datos para mostrar facturas adeudadas
      const outstandingInvoices: OutstandingInvoice[] = data.map((invoice: any) => ({
        id: invoice.id,
        number: invoice.number,
        type: invoice.invoiceType,
        dueDate: invoice.dueDate,
        description: invoice.description || 'SERVICIO FLETE',
        currencyValue: invoice.total,
        total: invoice.total,
        paidAmount: invoice.paidAmount || 0,
        balance: invoice.total - (invoice.paidAmount || 0),
        accumulatedBalance: invoice.total - (invoice.paidAmount || 0), // Calcular acumulado
      }));

      setState(prev => ({
        ...prev,
        outstandingInvoices,
        currentPage: pagination.currentPage,
        totalPages: pagination.totalPages,
        isLoading: false,
      }));
    } catch (error) {
      console.error('Error loading outstanding invoices:', error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [customerId, state.pageSize]);

  // Cargar movimientos de cuenta corriente
  const loadCustomerMovements = useCallback(async (page: number = 1) => {
    if (!customerId) return;

    setState(prev => ({ ...prev, isLoading: true }));
    
    try {
      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('limit', state.pageSize.toString());

      const response = await api.get(`/customers/${customerId}/ledger?${params.toString()}`);
      const { movements, pagination } = response.data;

      const customerMovements: CustomerMovement[] = movements.map((movement: any) => ({
        id: movement.id,
        date: movement.date,
        reference: movement.reference,
        type: movement.type,
        description: movement.type === 'INVOICE' ? 'SERVICIO FLETE' : movement.type,
        currencyValue: movement.debit || movement.credit,
        debit: movement.debit,
        credit: movement.credit,
        balance: movement.balance,
      }));

      setState(prev => ({
        ...prev,
        customerMovements,
        currentPage: pagination.currentPage,
        totalPages: pagination.totalPages,
        isLoading: false,
      }));
    } catch (error) {
      console.error('Error loading customer movements:', error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [customerId, state.pageSize]);

  // Cargar detalle completo del cliente
  const loadCustomerDetail = useCallback(async () => {
    if (!customerId) return;

    try {
      const response = await api.get(`/customers/${customerId}`);
      setState(prev => ({ ...prev, customerDetail: response.data }));
    } catch (error) {
      console.error('Error loading customer detail:', error);
    }
  }, [customerId]);

  // Cambiar página
  const setCurrentPage = useCallback((page: number, tab: string) => {
    setState(prev => ({ ...prev, currentPage: page }));
    
    if (tab === 'outstanding-invoices') {
      loadOutstandingInvoices(page);
    } else if (tab === 'customer-account') {
      loadCustomerMovements(page);
    }
  }, [loadOutstandingInvoices, loadCustomerMovements]);

  // Cargar datos cuando cambia el cliente
  useEffect(() => {
    if (customerId) {
      loadCustomerDetail();
      loadOutstandingInvoices(1);
      setState(prev => ({ ...prev, currentPage: 1 }));
    } else {
      setState(INITIAL_STATE);
    }
  }, [customerId, loadCustomerDetail, loadOutstandingInvoices]);

  return {
    ...state,
    loadOutstandingInvoices,
    loadCustomerMovements,
    loadCustomerDetail,
    setCurrentPage,
  };
};