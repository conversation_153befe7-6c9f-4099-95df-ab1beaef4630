import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';
import Customers from './pages/Customers';
import Suppliers from './pages/Suppliers';
import Treasury from './pages/Treasury';
import Inventory from './pages/Inventory';
import Accounting from './pages/Accounting';
import Settings from './pages/Settings';

function App() {
  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/tablero" element={<Dashboard />} />
          <Route path="/clientes" element={<Customers />} />
          <Route path="/proveedores" element={<Suppliers />} />
          <Route path="/tesoreria" element={<Treasury />} />
          <Route path="/inventario" element={<Inventory />} />
          <Route path="/contabilidad" element={<Accounting />} />
          <Route path="/configuracion" element={<Settings />} />
        </Routes>
      </Layout>
    </Router>
  );
}

export default App;