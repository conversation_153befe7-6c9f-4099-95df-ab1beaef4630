import React, { useState, useEffect } from 'react';
import { Plus, FileDown, Calendar } from 'lucide-react';
import { api } from '../services/api';

const Accounting: React.FC = () => {
  const [accounts, setAccounts] = useState<any[]>([]);
  const [entries, setEntries] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadAccountingData();
  }, []);

  const loadAccountingData = async () => {
    try {
      const [accountsRes, entriesRes] = await Promise.all([
        api.get('/accounting/chart-of-accounts'),
        api.get('/accounting/ledger'),
      ]);
      setAccounts(accountsRes.data);
      setEntries(entriesRes.data);
    } catch (error) {
      console.error('Error loading accounting data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 2,
    }).format(value);
  };

  if (loading) {
    return <div className="flex items-center justify-center h-64"><div className="text-gray-500">Cargando...</div></div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold text-gray-900">Contabilidad general</h1>
        <button className="btn-primary">
          <Calendar className="w-4 h-4 mr-2" />
          Calendario de vencimientos
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Panel de cuentas */}
        <div className="lg:col-span-1">
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium">Panel de cuentas</h3>
            </div>
            <div className="card-content">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Desde:</span>
                  <select className="text-sm border border-gray-300 rounded px-2 py-1">
                    <option>Seleccionar</option>
                  </select>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Hasta:</span>
                  <select className="text-sm border border-gray-300 rounded px-2 py-1">
                    <option>Seleccionar</option>
                  </select>
                </div>
                <button className="btn-primary w-full">Recalcular</button>
              </div>

              <div className="mt-6">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Cuenta</h4>
                <div className="space-y-1 max-h-64 overflow-y-auto">
                  {accounts.map((account) => (
                    <div key={account.id} className="flex items-center text-sm">
                      <span className="w-16 text-gray-500">{account.code}</span>
                      <span className="flex-1 text-gray-900">{account.name}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Sumas y saldos */}
        <div className="lg:col-span-3">
          <div className="card">
            <div className="card-header">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Sumas y saldos</h3>
                <div className="flex items-center space-x-2">
                  <label className="flex items-center">
                    <input type="checkbox" className="mr-2" />
                    <span className="text-sm">Mostrar Asientos de Ajuste por Inflación</span>
                  </label>
                  <div className="flex space-x-2">
                    <button className="btn-outline">
                      <FileDown className="w-4 h-4 mr-1" />
                      Reportes
                    </button>
                    <button className="btn-primary">
                      <Plus className="w-4 h-4 mr-1" />
                      Nuevo asiento a diario
                    </button>
                    <button className="btn-outline">
                      Importar asientos
                    </button>
                    <button className="btn-outline">
                      Cierres contables
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div className="card-content">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Cuenta</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Nro. cu...</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Saldo inicial</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Débito</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Crédito</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Saldo final</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    <tr>
                      <td className="px-6 py-4 text-sm">📁 Activo</td>
                      <td className="px-6 py-4 text-sm">0049-00000...</td>
                      <td className="px-6 py-4 text-sm">{formatCurrency(0)}</td>
                      <td className="px-6 py-4 text-sm">{formatCurrency(983780.35)}</td>
                      <td className="px-6 py-4 text-sm text-red-600">-{formatCurrency(983780.35)}</td>
                      <td className="px-6 py-4 text-sm">{formatCurrency(0)}</td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 text-sm">📁 PREVIO</td>
                      <td className="px-6 py-4 text-sm">0049-00000...</td>
                      <td className="px-6 py-4 text-sm">{formatCurrency(0)}</td>
                      <td className="px-6 py-4 text-sm">{formatCurrency(1309738.22)}</td>
                      <td className="px-6 py-4 text-sm text-red-600">-{formatCurrency(2674.11)}</td>
                      <td className="px-6 py-4 text-sm">{formatCurrency(1307064.11)}</td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 text-sm">📁 Patrimonio Neto</td>
                      <td className="px-6 py-4 text-sm">0052-00000...</td>
                      <td className="px-6 py-4 text-sm">{formatCurrency(0)}</td>
                      <td className="px-6 py-4 text-sm">{formatCurrency(468480.29)}</td>
                      <td className="px-6 py-4 text-sm text-red-600">-{formatCurrency(2142.59)}</td>
                      <td className="px-6 py-4 text-sm">{formatCurrency(466337.70)}</td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 text-sm">📁 Resultados</td>
                      <td className="px-6 py-4 text-sm">0059-00000...</td>
                      <td className="px-6 py-4 text-sm">{formatCurrency(0)}</td>
                      <td className="px-6 py-4 text-sm">{formatCurrency(585884.10)}</td>
                      <td className="px-6 py-4 text-sm text-red-600">-{formatCurrency(1758.81)}</td>
                      <td className="px-6 py-4 text-sm">{formatCurrency(584125.29)}</td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 text-sm">📁 Totales</td>
                      <td className="px-6 py-4 text-sm">-</td>
                      <td className="px-6 py-4 text-sm">{formatCurrency(0)}</td>
                      <td className="px-6 py-4 text-sm">{formatCurrency(23746021.96)}</td>
                      <td className="px-6 py-4 text-sm text-red-600">-{formatCurrency(23746021.96)}</td>
                      <td className="px-6 py-4 text-sm">{formatCurrency(0)}</td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-gray-500">
                  Mostrando 1 - 48 de 47
                </div>
                <div className="flex items-center space-x-2">
                  <button className="text-sm text-gray-500 hover:text-gray-700">‹</button>
                  <span className="text-sm text-gray-700">1</span>
                  <button className="text-sm text-gray-500 hover:text-gray-700">›</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Accounting;