import React, { useState, useEffect } from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { api } from '../services/api';

const Suppliers: React.FC = () => {
  const [suppliers, setSuppliers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSuppliers();
  }, []);

  const loadSuppliers = async () => {
    try {
      const response = await api.get('/suppliers');
      setSuppliers(response.data);
    } catch (error) {
      console.error('Error loading suppliers:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0,
    }).format(value);
  };

  // Mock data para gráficos
  const vencimientoData = [
    { period: 'Vencido', amount: 10000 },
    { period: 'Siguiente', amount: 0 },
    { period: '2 Semanas', amount: 0 },
    { period: '3 Semanas', amount: 0 },
    { period: '4 Semanas', amount: 0 },
  ];

  const facturaData = [
    { month: 'Sep', total: 20000, acumulado: 20000 },
    { month: 'Oct', total: 0, acumulado: 20000 },
    { month: 'Nov', total: 0, acumulado: 20000 },
    { month: 'Dic', total: 0, acumulado: 20000 },
    { month: 'Ene', total: 0, acumulado: 20000 },
    { month: 'Feb', total: 0, acumulado: 20000 },
    { month: 'Mar', total: 0, acumulado: 20000 },
    { month: 'Abr', total: 0, acumulado: 20000 },
    { month: 'May', total: 0, acumulado: 20000 },
    { month: 'Jun', total: 0, acumulado: 20000 },
    { month: 'Jul', total: 0, acumulado: 20000 },
    { month: 'Ago', total: 0, acumulado: 20000 },
  ];

  if (loading) {
    return <div className="flex items-center justify-center h-64"><div className="text-gray-500">Cargando...</div></div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold text-gray-900">Proveedores</h1>
      </div>

      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button className="border-b-2 border-primary-500 py-2 px-1 text-sm font-medium text-primary-600">
            Resumen proveedores
          </button>
          <button className="border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500">
            Gestión de proveedores
          </button>
        </nav>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Gráficos */}
        <div className="lg:col-span-2 space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Vencimiento de facturas */}
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-medium">Vencimiento de facturas</h3>
              </div>
              <div className="card-content">
                <ResponsiveContainer width="100%" height={200}>
                  <LineChart data={vencimientoData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="period" />
                    <YAxis />
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                    <Line type="monotone" dataKey="amount" stroke="#10b981" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
                <div className="text-center text-sm text-gray-500 mt-2">
                  Próximas 4 semanas
                </div>
              </div>
            </div>

            {/* Facturas proveedores */}
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-medium">Facturas proveedores</h3>
              </div>
              <div className="card-content">
                <ResponsiveContainer width="100%" height={200}>
                  <LineChart data={facturaData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                    <Line type="monotone" dataKey="total" stroke="#10b981" strokeWidth={2} name="Total egresos" />
                    <Line type="monotone" dataKey="acumulado" stroke="#6b7280" strokeWidth={2} name="Acumulado" />
                  </LineChart>
                </ResponsiveContainer>
                <div className="text-center text-sm text-gray-500 mt-2">
                  Últimos 12 meses
                </div>
              </div>
            </div>
          </div>

          {/* Facturas borrador */}
          <div className="card">
            <div className="card-header">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Facturas borrador</h3>
                <div className="flex space-x-2">
                  <button className="btn-primary">Listar facturas borrador</button>
                  <button className="btn-outline">Ingresar factura</button>
                </div>
              </div>
            </div>
            <div className="card-content">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Fecha</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Fecha Venc.</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Proveedor</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Nro Factura</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">T</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Descripción</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Neto</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">IVA</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Retenciones</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Total</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Pagado</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Saldo</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    <tr>
                      <td className="px-6 py-4 text-sm">2025-06...</td>
                      <td className="px-6 py-4 text-sm">27/06/2025</td>
                      <td className="px-6 py-4 text-sm">* RO - AR S.R.L. *</td>
                      <td className="px-6 py-4 text-sm">00002-00000...</td>
                      <td className="px-6 py-4 text-sm">FAC</td>
                      <td className="px-6 py-4 text-sm">Prueba Fac Prov 1</td>
                      <td className="px-6 py-4 text-sm">{formatCurrency(15500.00)}</td>
                      <td className="px-6 py-4 text-sm">{formatCurrency(3255.00)}</td>
                      <td className="px-6 py-4 text-sm">{formatCurrency(0)}</td>
                      <td className="px-6 py-4 text-sm">{formatCurrency(18755.00)}</td>
                      <td className="px-6 py-4 text-sm">{formatCurrency(0)}</td>
                      <td className="px-6 py-4 text-sm">{formatCurrency(18755.00)}</td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 text-sm">2025-08...</td>
                      <td className="px-6 py-4 text-sm">01/08/2025</td>
                      <td className="px-6 py-4 text-sm">Micro Sistemas S.A.U</td>
                      <td className="px-6 py-4 text-sm">00001-00000...</td>
                      <td className="px-6 py-4 text-sm">FAC</td>
                      <td className="px-6 py-4 text-sm">Prueba Fac Prov 1</td>
                      <td className="px-6 py-4 text-sm">{formatCurrency(30000.00)}</td>
                      <td className="px-6 py-4 text-sm">{formatCurrency(0)}</td>
                      <td className="px-6 py-4 text-sm">{formatCurrency(0)}</td>
                      <td className="px-6 py-4 text-sm">{formatCurrency(30000.00)}</td>
                      <td className="px-6 py-4 text-sm">{formatCurrency(0)}</td>
                      <td className="px-6 py-4 text-sm">{formatCurrency(30000.00)}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-gray-500">Mostrando 1 - 2 de 2</div>
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar - Reportes */}
        <div className="lg:col-span-1">
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium">Reportes</h3>
            </div>
            <div className="card-content">
              <div className="space-y-2">
                <button className="w-full text-left text-sm text-gray-700 hover:text-gray-900 py-1">
                  Listado de proveedores
                </button>
                <button className="w-full text-left text-sm text-gray-700 hover:text-gray-900 py-1">
                  Listado de facturas
                </button>
                <button className="w-full text-left text-sm text-gray-700 hover:text-gray-900 py-1">
                  Listado de pagos
                </button>
                <button className="w-full text-left text-sm text-gray-700 hover:text-gray-900 py-1">
                  IVA compras
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Suppliers;