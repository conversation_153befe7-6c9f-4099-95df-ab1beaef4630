import React, { useState, useEffect } from 'react';
import { Edit, Link, Info } from 'lucide-react';
import { Line<PERSON><PERSON>, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { api } from '../services/api';

const Treasury: React.FC = () => {
  const [accounts, setAccounts] = useState<any[]>([]);
  const [selectedAccount, setSelectedAccount] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadAccounts();
  }, []);

  const loadAccounts = async () => {
    try {
      const response = await api.get('/treasury/accounts');
      setAccounts(response.data);
      if (response.data.length > 0) {
        setSelectedAccount(response.data[0]);
      }
    } catch (error) {
      console.error('Error loading accounts:', error);
      // Mock data para demostración
      const mockAccounts = [
        {
          id: '1',
          code: 'CTA001',
          name: 'CAJA DOLARESAC',
          type: 'CASH',
          balance: 0,
        },
        {
          id: '2',
          code: 'CTA002',
          name: 'Banco Supervielle',
          type: 'BANK',
          balance: 5677622.47,
        },
        {
          id: '3',
          code: 'CTA003',
          name: 'BANCO SANTANDER',
          type: 'BANK',
          balance: -371476.24,
        },
      ];
      setAccounts(mockAccounts);
      setSelectedAccount(mockAccounts[0]);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 2,
    }).format(value);
  };

  // Mock data para el gráfico
  const chartData = [
    { month: 'Sep', ingresos: 0, egresos: 0, saldo: 0 },
    { month: 'Oct', ingresos: 0, egresos: 0, saldo: 0 },
    { month: 'Nov', ingresos: 0, egresos: 0, saldo: 0 },
    { month: 'Dic', ingresos: 0, egresos: 0, saldo: 0 },
    { month: 'Ene', ingresos: 0, egresos: 0, saldo: 0 },
    { month: 'Feb', ingresos: 0, egresos: 0, saldo: 0 },
    { month: 'Mar', ingresos: 0, egresos: 0, saldo: 0 },
    { month: 'Abr', ingresos: 0, egresos: 0, saldo: 0 },
    { month: 'May', ingresos: 0, egresos: 0, saldo: 0 },
    { month: 'Jun', ingresos: 0, egresos: 0, saldo: 0 },
    { month: 'Jul', ingresos: 0, egresos: 0, saldo: 0 },
    { month: 'Ago', ingresos: 0, egresos: 0, saldo: 0 },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Cargando...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold text-gray-900">Tesorería</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Lista de Cajas/Bancos */}
        <div className="lg:col-span-1">
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium">Lista de Cajas/Bancos</h3>
            </div>
            <div className="card-content">
              <div className="space-y-2">
                {accounts.map((account) => (
                  <div
                    key={account.id}
                    className={`p-3 rounded-lg cursor-pointer border ${
                      selectedAccount?.id === account.id
                        ? 'bg-primary-50 border-primary-200'
                        : 'hover:bg-gray-50 border-gray-200'
                    }`}
                    onClick={() => setSelectedAccount(account)}
                  >
                    <div className="font-medium text-gray-900">{account.name}</div>
                    <div className="text-sm text-gray-500">
                      Saldo ME: USD {formatCurrency(0)}
                    </div>
                    <div className="text-sm text-gray-900">
                      Saldo: {formatCurrency(account.balance)}
                    </div>
                    <div className="flex items-center space-x-2 mt-2">
                      <button className="btn-outline text-xs px-2 py-1">
                        Editar
                      </button>
                      {account.type === 'BANK' && (
                        <>
                          <button className="btn-outline text-xs px-2 py-1">
                            Conciliar
                          </button>
                          <button className="btn-primary text-xs px-2 py-1">
                            Conectar
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                ))}
                
                <div className="p-3 rounded-lg border border-gray-200">
                  <div className="font-medium text-gray-900">Caja en Pesos</div>
                  <div className="text-sm text-gray-900">
                    Saldo: {formatCurrency(-924343.70)}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Panel principal */}
        <div className="lg:col-span-3">
          <div className="space-y-6">
            {/* Gráfico */}
            <div className="card">
              <div className="card-header">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">
                    Saldo / Ingresos / Egresos {selectedAccount?.name}
                  </h3>
                  <div className="flex items-center space-x-4">
                    <button className="text-sm text-gray-600 hover:text-gray-900">Mensual</button>
                    <button className="text-sm text-gray-600 hover:text-gray-900">Trimestral</button>
                    <button className="text-sm text-gray-600 hover:text-gray-900">Anual</button>
                  </div>
                </div>
              </div>
              <div className="card-content">
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                    <Line type="monotone" dataKey="ingresos" stroke="#10b981" strokeWidth={2} name="Total ingresos" />
                    <Line type="monotone" dataKey="egresos" stroke="#f59e0b" strokeWidth={2} name="Total egresos" />
                    <Line type="monotone" dataKey="saldo" stroke="#6b7280" strokeWidth={2} name="Saldo" />
                  </LineChart>
                </ResponsiveContainer>
                <div className="text-center text-sm text-gray-500 mt-2">
                  Últimos 12 meses
                </div>
              </div>
            </div>

            {/* Movimientos */}
            <div className="card">
              <div className="card-header">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Movimientos {selectedAccount?.name}</h3>
                  <div className="flex items-center space-x-2">
                    <button className="text-sm text-gray-600 hover:text-gray-900">Mensual</button>
                    <button className="text-sm text-gray-600 hover:text-gray-900">Trimestral</button>
                    <button className="text-sm text-gray-600 hover:text-gray-900">Anual</button>
                  </div>
                </div>
              </div>
              <div className="card-content">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Fecha
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Cliente/Proveedor
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Tipo
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Nro Che...
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Descripción
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Importe ME
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Ingre...
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Egre...
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Saldo
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      <tr>
                        <td colSpan={9} className="px-6 py-4 text-center text-gray-500">
                          Mostrando 0 - 48 de 2
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-gray-500">
                    Página 0 de 1
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar derecho - Reportes */}
        <div className="lg:col-span-1 lg:col-start-4 lg:row-start-1 lg:row-span-2">
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium">Reportes</h3>
            </div>
            <div className="card-content">
              <div className="space-y-2">
                <button className="w-full text-left text-sm text-gray-700 hover:text-gray-900 py-1">
                  Reporte de cheques en cartera
                </button>
                <button className="w-full text-left text-sm text-gray-700 hover:text-gray-900 py-1">
                  Reporte de cheques emitidos diferidos
                </button>
                <button className="w-full text-left text-sm text-gray-700 hover:text-gray-900 py-1">
                  Reporte de seguimiento de cheques
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Treasury;