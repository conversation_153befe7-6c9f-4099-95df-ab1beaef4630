import React, { useState } from 'react';
import { useCustomers } from '../hooks/useCustomers';
import { useCustomerDetail } from '../hooks/useCustomerDetail';
import { usePermissions } from '../hooks/usePermissions';
import { CustomersList } from '../components/customers/CustomersList';
import { CustomersDetail } from '../components/customers/CustomersDetail';
import { InvoiceModal } from '../components/customers/InvoiceModal';
import { Customer, OutstandingInvoice, CustomerMovement, CustomerDetail, Contact } from '../types/customer.types';
import { CreateInvoiceData } from '../types/invoice.types';
import { api } from '../services/api';

const Customers: React.FC = () => {
  const [activeMainTab, setActiveMainTab] = useState('resumen-clientes');
  
  const {
    customers,
    selectedCustomer,
    activeFilter,
    searchTerm,
    currentPage,
    totalPages,
    activeTab,
    isLoading,
    isInvoiceModalOpen,
    setActiveFilter,
    setSearchTerm,
    selectCustomer,
    loadCustomerDetail,
    setActiveTab,
    setCurrentPage,
    openInvoiceModal,
    closeInvoiceModal,
  } = useCustomers();

  const {
    outstandingInvoices,
    customerMovements,
    customerDetail,
    isLoading: detailLoading,
    currentPage: detailCurrentPage,
    totalPages: detailTotalPages,
    setCurrentPage: setDetailCurrentPage,
  } = useCustomerDetail(selectedCustomer?.id);

  const { permissions } = usePermissions();

  // Handlers para interacciones específicas
  const handleCustomerSelect = (customer: Customer) => {
    selectCustomer(customer);
  };

  const handleCustomerDoubleClick = (customer: Customer) => {
    loadCustomerDetail(customer);
  };

  const handleInvoiceDoubleClick = (invoice: OutstandingInvoice) => {
    console.log('Opening invoice detail:', invoice);
    // TODO: Implementar modal de detalle de factura
  };

  const handleMovementDoubleClick = (movement: CustomerMovement) => {
    console.log('Opening movement detail:', movement);
    // TODO: Implementar navegación al documento origen
  };

  const handleSaveCustomer = async (data: Partial<CustomerDetail>) => {
    if (!selectedCustomer) return;
    
    try {
      await api.put(`/customers/${selectedCustomer.id}`, data);
      console.log('Customer saved successfully');
      // TODO: Mostrar notificación de éxito
    } catch (error) {
      console.error('Error saving customer:', error);
      // TODO: Mostrar notificación de error
    }
  };

  const handleDianConsult = async (nit: string) => {
    try {
      // TODO: Implementar consulta DIAN
      console.log('Consulting DIAN for NIT:', nit);
    } catch (error) {
      console.error('Error consulting DIAN:', error);
    }
  };

  const handleAddContact = async (contact: Omit<Contact, 'id'>) => {
    if (!selectedCustomer) return;
    
    try {
      await api.post(`/customers/${selectedCustomer.id}/contacts`, contact);
      console.log('Contact added successfully');
      // TODO: Recargar datos del cliente
    } catch (error) {
      console.error('Error adding contact:', error);
    }
  };

  const handleSaveInvoice = async (invoiceData: CreateInvoiceData) => {
    if (!selectedCustomer) return;
    
    try {
      const response = await api.post('/ar/invoices', {
        customerId: selectedCustomer.id,
        date: invoiceData.fechaFactura,
        dueDate: invoiceData.fechaVencimiento,
        number: invoiceData.numeroFactura,
        subtotal: invoiceData.netoGravado + invoiceData.netoNoGravado,
        taxAmount: invoiceData.totalIVA,
        total: invoiceData.totalFactura,
        status: invoiceData.estado === 'Aprobada' ? 'APPROVED' : 'DRAFT',
        currency: 'COP',
        items: invoiceData.items.filter(item => item.quantity > 0).map(item => ({
          description: item.description,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          taxRate: item.ivaPercentage,
          taxAmount: (item.quantity * item.unitPrice * item.ivaPercentage) / 100,
          total: item.subtotal,
        })),
      });
      
      console.log('Invoice created successfully:', response.data);
      closeInvoiceModal();
      // TODO: Recargar facturas adeudadas
    } catch (error) {
      console.error('Error creating invoice:', error);
      // TODO: Mostrar notificación de error
    }
  };



  const mainTabs = [
    { id: 'resumen-clientes', label: 'Resumen clientes' },
    { id: 'gestion-clientes', label: 'Gestión de clientes' },
    { id: 'importacion-facturas', label: 'Importación de facturas' },
    { id: 'mercado-pago', label: 'Mercado Pago' },
  ];

  return (
    <div className="space-y-6 h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold text-gray-900">Clientes</h1>
        <div className="flex items-center space-x-4">
          <button className="btn-outline">
            ¿Querés emitir Factura electrónica?
          </button>
          <button className="btn-primary">
            Configuralo ahora
          </button>
          <button className="btn-primary">
            Calendario de vencimientos
          </button>
        </div>
      </div>

      {/* Main Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {mainTabs.map((tab) => (
            <button
              key={tab.id}
              className={`py-2 px-1 text-sm font-medium ${
                activeMainTab === tab.id
                  ? 'border-b-2 border-primary-500 text-primary-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveMainTab(tab.id)}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Content - Layout de dos paneles fijo */}
      {activeMainTab === 'resumen-clientes' && (
        <div className="flex-1 grid grid-cols-1 lg:grid-cols-3 gap-6 min-h-0">
          {/* Panel izquierdo - Lista de clientes */}
          <div className="lg:col-span-1 min-h-0">
            <CustomersList
              customers={customers}
              selectedCustomer={selectedCustomer}
              activeFilter={activeFilter}
              searchTerm={searchTerm}
              loading={isLoading}
              currentPage={currentPage}
              totalPages={totalPages}
              onCustomerSelect={handleCustomerSelect}
              onCustomerDoubleClick={handleCustomerDoubleClick}
              onActiveFilterChange={setActiveFilter}
              onSearchChange={setSearchTerm}
              onPageChange={setCurrentPage}
            />
          </div>

          {/* Panel derecho - Detalle del cliente */}
          <div className="lg:col-span-2 min-h-0">
            <CustomersDetail
              customer={selectedCustomer}
              customerDetail={customerDetail}
              activeTab={activeTab}
              outstandingInvoices={outstandingInvoices}
              customerMovements={customerMovements}
              loading={detailLoading}
              currentPage={detailCurrentPage}
              totalPages={detailTotalPages}
              onTabChange={setActiveTab}
              onPageChange={setDetailCurrentPage}
              onInvoiceDoubleClick={handleInvoiceDoubleClick}
              onMovementDoubleClick={handleMovementDoubleClick}
              onAddInvoice={openInvoiceModal}
              onAddReceipt={() => console.log('Add receipt')}
              onSaveCustomer={handleSaveCustomer}
              onDianConsult={handleDianConsult}
              onAddContact={handleAddContact}
              canAddInvoice={permissions.canAddInvoice}
              canAddReceipt={permissions.canAddReceipt}
            />
          </div>
        </div>
      )}

      {/* Otros tabs principales */}
      {activeMainTab === 'gestion-clientes' && (
        <div className="text-center py-8 text-gray-500">
          Gestión de clientes - En desarrollo
        </div>
      )}



      {activeMainTab === 'importacion-facturas' && (
        <div className="text-center py-8 text-gray-500">
          Importación de facturas - En desarrollo
        </div>
      )}

      {activeMainTab === 'mercado-pago' && (
        <div className="text-center py-8 text-gray-500">
          Mercado Pago - En desarrollo
        </div>
      )}

      {/* Modal de factura */}
      {isInvoiceModalOpen && selectedCustomer && (
        <InvoiceModal
          isOpen={isInvoiceModalOpen}
          customer={selectedCustomer}
          onClose={closeInvoiceModal}
          onSave={handleSaveInvoice}
        />
      )}
    </div>
  );
};

export default Customers;