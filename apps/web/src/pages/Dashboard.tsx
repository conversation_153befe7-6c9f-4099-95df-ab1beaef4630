import React, { useState, useEffect } from 'react';
import { TrendingUp, TrendingDown, DollarSign, Users, Building2, Package } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';
import KpiCard from '../components/KpiCard';
import { api } from '../services/api';

const Dashboard: React.FC = () => {
  const [kpis, setKpis] = useState<any>({});
  const [incomesExpenses, setIncomesExpenses] = useState<any[]>([]);
  const [cashFlow, setCashFlow] = useState<any[]>([]);
  const [expenses, setExpenses] = useState<any>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      const [kpisRes, seriesRes, expensesRes] = await Promise.all([
        api.get('/dashboard/kpis'),
        api.get('/dashboard/series?chart=incomes_expenses'),
        api.get('/dashboard/expenses'),
      ]);

      setKpis(kpisRes.data);
      setIncomesExpenses(seriesRes.data.series || []);
      setExpenses(expensesRes.data);
      
      // Mock data para cajas y bancos
      setCashFlow([
        { month: 'Sep', ingresos: 0, egresos: 0, saldo: 0 },
        { month: 'Oct', ingresos: 0, egresos: 0, saldo: 0 },
        { month: 'Nov', ingresos: 0, egresos: 0, saldo: 0 },
        { month: 'Dic', ingresos: 0, egresos: 0, saldo: 0 },
        { month: 'Ene', ingresos: 0, egresos: 0, saldo: 0 },
        { month: 'Feb', ingresos: 0, egresos: 0, saldo: 0 },
        { month: 'Mar', ingresos: 0, egresos: 0, saldo: 0 },
        { month: 'Abr', ingresos: 0, egresos: 0, saldo: 0 },
        { month: 'May', ingresos: 0, egresos: 0, saldo: 0 },
        { month: 'Jun', ingresos: 0, egresos: 0, saldo: 0 },
        { month: 'Jul', ingresos: 0, egresos: 0, saldo: 0 },
        { month: 'Ago', ingresos: 2500000, egresos: 0, saldo: 2500000 },
      ]);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0,
    }).format(value);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Cargando...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Tablero de control</h1>
          <p className="text-gray-600 mt-1">Hola, Andrés Morelo 👋</p>
        </div>
      </div>

      {/* KPIs */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <KpiCard
          title="Total ventas del mes"
          value={formatCurrency(kpis.sales?.current || *********.37)}
          change="-92.51%"
          trend="down"
          icon={DollarSign}
        />
        <KpiCard
          title="Total compras del mes"
          value={formatCurrency(kpis.purchases?.current || 3501494.12)}
          change="+90.66%"
          trend="up"
          icon={Building2}
        />
        <KpiCard
          title="Facturas por cobrar"
          value={formatCurrency(kpis.accountsReceivable?.current || *********.47)}
          change=""
          icon={Users}
        />
        <KpiCard
          title="Facturas por pagar"
          value={formatCurrency(kpis.accountsPayable?.current || 3622494.12)}
          change=""
          icon={Package}
        />
      </div>

      {/* Charts Row 1 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Ingresos y gastos */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Ingresos y gastos</h3>
              <select className="text-sm border border-gray-300 rounded px-2 py-1">
                <option>Mensual</option>
                <option>Trimestral</option>
                <option>Anual</option>
              </select>
            </div>
          </div>
          <div className="card-content">
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={incomesExpenses}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                <Line type="monotone" dataKey="ingresos" stroke="#10b981" strokeWidth={2} />
                <Line type="monotone" dataKey="gastos" stroke="#ef4444" strokeWidth={2} />
                <Line type="monotone" dataKey="saldo" stroke="#6b7280" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Cajas y bancos */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Cajas y bancos</h3>
              <select className="text-sm border border-gray-300 rounded px-2 py-1">
                <option>Mensual</option>
                <option>Trimestral</option>
                <option>Anual</option>
              </select>
            </div>
          </div>
          <div className="card-content">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={cashFlow}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                <Bar dataKey="ingresos" fill="#10b981" />
                <Bar dataKey="egresos" fill="#ef4444" />
                <Bar dataKey="saldo" fill="#6b7280" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Charts Row 2 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Facturas */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Facturas</h3>
              <div className="flex space-x-2">
                <button className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded">Por cobrar</button>
                <button className="px-3 py-1 text-sm text-gray-600 rounded">Por pagar</button>
              </div>
              <select className="text-sm border border-gray-300 rounded px-2 py-1">
                <option>Trimestral</option>
                <option>Mensual</option>
                <option>Anual</option>
              </select>
            </div>
          </div>
          <div className="card-content">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={[
                { month: 'Ene-Mar', ingresos: 0, saldo: 0 },
                { month: 'Abr-Jun', ingresos: 0, saldo: 0 },
                { month: 'Jul-Sep', ingresos: 450000000, saldo: 450000000 },
              ]}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                <Bar dataKey="ingresos" fill="#10b981" />
                <Bar dataKey="saldo" fill="#6b7280" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Distribución de gastos */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Distribución de gastos</h3>
              <select className="text-sm border border-gray-300 rounded px-2 py-1">
                <option>Semanal</option>
                <option>Mensual</option>
                <option>Trimestral</option>
              </select>
            </div>
          </div>
          <div className="card-content">
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="w-32 h-32 rounded-full bg-finantic-green mx-auto mb-4 flex items-center justify-center">
                  <span className="text-white text-2xl font-bold">370</span>
                </div>
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-3 h-3 bg-finantic-green rounded-full"></div>
                  <span className="text-sm text-gray-600">Costo de Mercadería... 100%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;