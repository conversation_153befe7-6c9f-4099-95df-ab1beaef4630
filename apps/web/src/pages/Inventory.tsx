import React, { useState, useEffect } from 'react';
import { Search, Plus, FileDown } from 'lucide-react';
import { api } from '../services/api';

const Inventory: React.FC = () => {
  const [items, setItems] = useState<any[]>([]);
  const [search, setSearch] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadItems();
  }, []);

  const loadItems = async () => {
    try {
      const response = await api.get('/items');
      setItems(response.data);
    } catch (error) {
      console.error('Error loading items:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 2,
    }).format(value);
  };

  const filteredItems = items.filter(item =>
    item.name.toLowerCase().includes(search.toLowerCase()) ||
    item.code.toLowerCase().includes(search.toLowerCase())
  );

  if (loading) {
    return <div className="flex items-center justify-center h-64"><div className="text-gray-500">Cargando...</div></div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold text-gray-900">Inventario</h1>
      </div>

      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button className="border-b-2 border-primary-500 py-2 px-1 text-sm font-medium text-primary-600">
            Items de inventario
          </button>
          <button className="border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500">
            Configuración de inventario
          </button>
          <button className="border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500">
            Listas de precios
          </button>
        </nav>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Filtros y acciones */}
        <div className="lg:col-span-1">
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium">Filtros</h3>
            </div>
            <div className="card-content space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Buscar por código o descripción
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    className="input pl-10"
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Actualizar precios
                </label>
                <select className="input">
                  <option>Seleccionar</option>
                </select>
              </div>

              <div className="flex space-x-2">
                <button className="btn-outline flex-1">
                  <FileDown className="w-4 h-4 mr-1" />
                  Reportes
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Lista de items */}
        <div className="lg:col-span-3">
          <div className="card">
            <div className="card-header">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">
                  Listado de ítems de inventario (doble click para seleccionar ítem)
                </h3>
                <div className="flex space-x-2">
                  <button className="btn-primary">
                    <Plus className="w-4 h-4 mr-1" />
                    Agregar
                  </button>
                  <button className="btn-outline">
                    Importar ítems
                  </button>
                </div>
              </div>
            </div>
            <div className="card-content">
              {/* Tabs */}
              <div className="border-b border-gray-200 mb-4">
                <nav className="-mb-px flex space-x-8">
                  <button className="border-b-2 border-primary-500 py-2 px-1 text-sm font-medium text-primary-600">
                    Movimientos
                  </button>
                  <button className="border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500">
                    Información general
                  </button>
                  <button className="border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500">
                    Disponibilidad / Depósito
                  </button>
                  <button className="border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500">
                    Remitos
                  </button>
                </nav>
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Código</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Descripción</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Tipo</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">UM</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">P. Venta</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Cto. Calcula...</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Dispo...</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Descripción</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">TipoDoc</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Precio</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Cantidad</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredItems.map((item) => (
                      <tr key={item.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 text-sm font-medium text-gray-900">{item.code}</td>
                        <td className="px-6 py-4 text-sm text-gray-900">{item.name}</td>
                        <td className="px-6 py-4 text-sm text-gray-500">{item.type === 'PRODUCT' ? 'P' : 'S'}</td>
                        <td className="px-6 py-4 text-sm text-gray-500">{item.unit}</td>
                        <td className="px-6 py-4 text-sm text-gray-900">{formatCurrency(item.price)}</td>
                        <td className="px-6 py-4 text-sm text-gray-900">{formatCurrency(0)}</td>
                        <td className="px-6 py-4 text-sm text-gray-900">{formatCurrency(0)}</td>
                        <td className="px-6 py-4 text-sm text-gray-500">{item.description || '-'}</td>
                        <td className="px-6 py-4 text-sm text-gray-500">-</td>
                        <td className="px-6 py-4 text-sm text-gray-900">{formatCurrency(item.price)}</td>
                        <td className="px-6 py-4 text-sm text-gray-900">0</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-gray-500">
                  Mostrando 1 - {filteredItems.length} de {items.length}
                </div>
                <div className="text-sm text-gray-500">
                  Sin datos para mostrar
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Inventory;