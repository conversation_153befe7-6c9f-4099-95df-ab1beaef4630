import React, { useState, useEffect } from 'react';
import { Save, Upload } from 'lucide-react';
import { api } from '../services/api';

const Settings: React.FC = () => {
  const [activeTab, setActiveTab] = useState('general');
  const [companyData, setCompanyData] = useState<any>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadCompanySettings();
  }, []);

  const loadCompanySettings = async () => {
    try {
      const response = await api.get('/settings/company');
      setCompanyData(response.data);
    } catch (error) {
      console.error('Error loading company settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      await api.put('/settings/company', companyData);
      alert('Configuración guardada exitosamente');
    } catch (error) {
      console.error('Error saving settings:', error);
      alert('Error al guardar la configuración');
    }
  };

  const tabs = [
    { id: 'general', name: 'Datos generales' },
    { id: 'tax', name: 'Datos Impositivos' },
    { id: 'customers', name: 'Clientes/Proveedores' },
    { id: 'documents', name: 'Talonarios' },
    { id: 'clients', name: 'Portal Clientes' },
    { id: 'treasury', name: 'Tesorería' },
    { id: 'integrations', name: 'Integraciones' },
    { id: 'costs', name: 'Centros de Costos' },
  ];

  if (loading) {
    return <div className="flex items-center justify-center h-64"><div className="text-gray-500">Cargando...</div></div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold text-gray-900">Configuración de la Empresa</h1>
      </div>

      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8 overflow-x-auto">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`whitespace-nowrap border-b-2 py-2 px-1 text-sm font-medium ${
                activeTab === tab.id
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Formulario */}
        <div className="lg:col-span-2">
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium">
                {activeTab === 'general' && 'Datos generales'}
                {activeTab === 'tax' && 'Datos Impositivos'}
                {activeTab === 'customers' && 'Clientes/Proveedores'}
                {activeTab === 'documents' && 'Talonarios'}
                {activeTab === 'clients' && 'Portal Clientes'}
                {activeTab === 'treasury' && 'Tesorería'}
                {activeTab === 'integrations' && 'Integraciones'}
                {activeTab === 'costs' && 'Centros de Costos'}
              </h3>
            </div>
            <div className="card-content">
              {activeTab === 'general' && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Nombre:</label>
                      <input
                        type="text"
                        className="input"
                        value={companyData.name || ''}
                        onChange={(e) => setCompanyData({...companyData, name: e.target.value})}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Razón social:</label>
                      <input
                        type="text"
                        className="input"
                        value={companyData.tradeName || ''}
                        onChange={(e) => setCompanyData({...companyData, tradeName: e.target.value})}
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Domicilio:</label>
                    <input
                      type="text"
                      className="input"
                      value={companyData.address || ''}
                      onChange={(e) => setCompanyData({...companyData, address: e.target.value})}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Localidad:</label>
                      <input
                        type="text"
                        className="input"
                        value={companyData.city || ''}
                        onChange={(e) => setCompanyData({...companyData, city: e.target.value})}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Cod. Postal:</label>
                      <input
                        type="text"
                        className="input"
                        value="3153"
                        readOnly
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Provincia:</label>
                      <select className="input">
                        <option>VICTORIA</option>
                      </select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">País:</label>
                      <input
                        type="text"
                        className="input"
                        value={companyData.country || 'CO'}
                        onChange={(e) => setCompanyData({...companyData, country: e.target.value})}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Teléfono:</label>
                      <input
                        type="text"
                        className="input"
                        value={companyData.phone || ''}
                        onChange={(e) => setCompanyData({...companyData, phone: e.target.value})}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Email:</label>
                      <input
                        type="email"
                        className="input"
                        value={companyData.email || ''}
                        onChange={(e) => setCompanyData({...companyData, email: e.target.value})}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">CUIT:</label>
                      <input
                        type="text"
                        className="input"
                        value={companyData.nit || ''}
                        onChange={(e) => setCompanyData({...companyData, nit: e.target.value})}
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">CUIT:</label>
                    <input
                      type="text"
                      className="input"
                      value="30681088596"
                      readOnly
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Número IIBB:</label>
                    <input
                      type="text"
                      className="input"
                    />
                  </div>

                  <div>
                    <label className="flex items-center">
                      <input type="checkbox" className="mr-2" />
                      <span className="text-sm text-gray-700">Inscripto en Convenio Multilateral</span>
                    </label>
                  </div>

                  <button onClick={handleSave} className="btn-primary">
                    <Save className="w-4 h-4 mr-2" />
                    Guardar datos
                  </button>
                </div>
              )}

              {activeTab !== 'general' && (
                <div className="text-center py-8 text-gray-500">
                  Configuración de {tabs.find(t => t.id === activeTab)?.name} - En desarrollo
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Logo */}
        <div className="lg:col-span-1">
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium">Logo</h3>
            </div>
            <div className="card-content">
              <div className="text-center">
                <div className="w-48 h-32 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg mx-auto mb-4 flex items-center justify-center">
                  <span className="text-white text-4xl font-bold">F</span>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Logo Actual:</label>
                    <input
                      type="text"
                      className="input"
                      value="11676.jpg"
                      readOnly
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Ancho en milímetros:</label>
                    <input
                      type="number"
                      className="input"
                      value="30"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Altura en milímetros:</label>
                    <input
                      type="number"
                      className="input"
                      value="20"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Archivo del Logo:</label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="text"
                        className="input flex-1"
                        placeholder="Seleccionar una imagen del Logo (.png/.gif/.jpeg)"
                      />
                      <button className="btn-outline">
                        <Upload className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                  
                  <button className="btn-primary w-full">
                    Guardar logo
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;