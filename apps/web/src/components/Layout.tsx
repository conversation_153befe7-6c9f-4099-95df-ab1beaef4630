import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  BarChart3, 
  Users, 
  Building2, 
  Banknote, 
  Package, 
  Calculator, 
  Settings,
  Calendar,
  ChevronDown,
  Bell
} from 'lucide-react';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation();

  const navigation = [
    { name: 'Tablero', href: '/tablero', icon: BarChart3 },
    { name: 'Clientes', href: '/clientes', icon: Users },
    { name: 'Tesorería', href: '/tesoreria', icon: Banknote },
    { name: 'Proveedores', href: '/proveedores', icon: Building2 },
    { name: 'Inventario', href: '/inventario', icon: Package },
    { name: 'Contabilidad', href: '/contabilidad', icon: Calculator },
    { name: 'Configuración', href: '/configuracion', icon: Settings },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">F</span>
              </div>
              <span className="text-xl font-semibold text-gray-900">Finantic</span>
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <ChevronDown className="w-4 h-4" />
              <span>Empresa de Prueba</span>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <button className="text-gray-600 hover:text-gray-900">
              Referir y ganar
            </button>
            <button className="text-gray-600 hover:text-gray-900">
              Liquidar sueldos
            </button>
            <button className="text-gray-600 hover:text-gray-900">
              Ayuda
            </button>
            <button className="text-gray-600 hover:text-gray-900">
              Mi cuenta
            </button>
            <button className="p-2 text-gray-600 hover:text-gray-900 relative">
              <Bell className="w-5 h-5" />
            </button>
            <button className="btn-primary">
              <Calendar className="w-4 h-4 mr-2" />
              Calendario de vencimientos
            </button>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <aside className="w-64 bg-white border-r border-gray-200 min-h-screen">
          <nav className="p-4">
            <div className="sidebar-nav">
              {navigation.map((item) => {
                const Icon = item.icon;
                const isActive = location.pathname === item.href || 
                  (item.href === '/tablero' && location.pathname === '/');
                
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`sidebar-nav-item ${isActive ? 'active' : ''}`}
                  >
                    <Icon className="w-5 h-5" />
                    <span>{item.name}</span>
                  </Link>
                );
              })}
            </div>
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-6">
          {children}
        </main>
      </div>
    </div>
  );
};

export default Layout;