import React from 'react';
import { CustomerMovement } from '../../types/customer.types';
import { DataTable } from './DataTable';

interface CustomerAccountProps {
  customerId?: string;
  movements: CustomerMovement[];
  loading: boolean;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  onMovementDoubleClick: (movement: CustomerMovement) => void;
}

export const CustomerAccount: React.FC<CustomerAccountProps> = ({
  customerId,
  movements,
  loading,
  currentPage,
  totalPages,
  onPageChange,
  onMovementDoubleClick,
}) => {
  const columns = [
    {
      key: 'date',
      label: 'Fecha',
      sortable: true,
      render: (value: string) => new Date(value).toLocaleDateString('es-CO'),
    },
    {
      key: 'reference',
      label: 'Comprobante',
      sortable: true,
    },
    {
      key: 'type',
      label: 'Tipo Comp',
      sortable: true,
      render: (value: string) => {
        const typeMap: { [key: string]: string } = {
          'INVOICE': 'FAV',
          'RECEIPT': 'REC',
          'CREDIT_NOTE': 'NCR',
        };
        return typeMap[value] || value;
      },
    },
    {
      key: 'description',
      label: 'Descripción',
      sortable: false,
    },
    {
      key: 'currencyValue',
      label: 'Valor ME',
      sortable: true,
      render: (value: number) => new Intl.NumberFormat('es-CO', {
        style: 'currency',
        currency: 'COP',
        minimumFractionDigits: 2,
      }).format(value),
    },
    {
      key: 'debit',
      label: 'Débito',
      sortable: true,
      render: (value: number) => value > 0 ? new Intl.NumberFormat('es-CO', {
        style: 'currency',
        currency: 'COP',
        minimumFractionDigits: 2,
      }).format(value) : '0,00',
    },
    {
      key: 'credit',
      label: 'Crédito',
      sortable: true,
      render: (value: number) => value > 0 ? new Intl.NumberFormat('es-CO', {
        style: 'currency',
        currency: 'COP',
        minimumFractionDigits: 2,
      }).format(value) : '0,00',
    },
    {
      key: 'balance',
      label: 'Saldo adeud.',
      sortable: true,
      render: (value: number) => new Intl.NumberFormat('es-CO', {
        style: 'currency',
        currency: 'COP',
        minimumFractionDigits: 2,
      }).format(value),
    },
  ];

  if (!customerId) {
    return (
      <div className="text-center py-8 text-gray-500">
        Seleccionar cliente de la lista
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <DataTable
        columns={columns}
        data={movements}
        loading={loading}
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={onPageChange}
        onRowDoubleClick={onMovementDoubleClick}
        emptyMessage="Cuenta Corriente de Cliente (doble click para seleccionar ítem)"
        stickyHeader={true}
      />
    </div>
  );
};