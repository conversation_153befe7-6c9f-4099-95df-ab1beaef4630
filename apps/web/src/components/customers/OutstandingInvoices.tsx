import React from 'react';
import { OutstandingInvoice } from '../../types/customer.types';
import { DataTable } from './DataTable';

interface OutstandingInvoicesProps {
  customerId?: string;
  invoices: OutstandingInvoice[];
  loading: boolean;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  onInvoiceDoubleClick: (invoice: OutstandingInvoice) => void;
  onAddInvoice: () => void;
  onAddReceipt: () => void;
  canAddInvoice: boolean;
  canAddReceipt: boolean;
}

export const OutstandingInvoices: React.FC<OutstandingInvoicesProps> = ({
  customerId,
  invoices,
  loading,
  currentPage,
  totalPages,
  onPageChange,
  onInvoiceDoubleClick,
  onAddInvoice,
  onAddReceipt,
  canAddInvoice,
  canAddReceipt,
}) => {
  const columns = [
    {
      key: 'number',
      label: 'Nro. Comprob.',
      sortable: true,
    },
    {
      key: 'type',
      label: 'Tipo',
      sortable: true,
    },
    {
      key: 'dueDate',
      label: 'Fecha pago',
      sortable: true,
      render: (value: string) => value ? new Date(value).toLocaleDateString('es-CO') : '-',
    },
    {
      key: 'description',
      label: 'Descripción',
      sortable: false,
    },
    {
      key: 'currencyValue',
      label: 'Valor ME',
      sortable: true,
      render: (value: number) => new Intl.NumberFormat('es-CO', {
        style: 'currency',
        currency: 'COP',
        minimumFractionDigits: 2,
      }).format(value),
    },
    {
      key: 'total',
      label: 'Total',
      sortable: true,
      render: (value: number) => new Intl.NumberFormat('es-CO', {
        style: 'currency',
        currency: 'COP',
        minimumFractionDigits: 2,
      }).format(value),
    },
    {
      key: 'paidAmount',
      label: 'Cobrado',
      sortable: true,
      render: (value: number) => new Intl.NumberFormat('es-CO', {
        style: 'currency',
        currency: 'COP',
        minimumFractionDigits: 2,
      }).format(value),
    },
    {
      key: 'balance',
      label: 'Saldo',
      sortable: true,
      render: (value: number) => new Intl.NumberFormat('es-CO', {
        style: 'currency',
        currency: 'COP',
        minimumFractionDigits: 2,
      }).format(value),
    },
    {
      key: 'accumulatedBalance',
      label: 'Saldo acumul.',
      sortable: true,
      render: (value: number) => new Intl.NumberFormat('es-CO', {
        style: 'currency',
        currency: 'COP',
        minimumFractionDigits: 2,
      }).format(value),
    },
  ];

  if (!customerId) {
    return (
      <div className="text-center py-8 text-gray-500">
        Seleccionar cliente de la lista
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Botones de acción */}
      <div className="flex space-x-2">
        <button
          onClick={onAddInvoice}
          disabled={!canAddInvoice}
          className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Agregar factura
        </button>
        <button
          onClick={onAddReceipt}
          disabled={!canAddReceipt}
          className="btn-outline disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Agregar cobranza
        </button>
        <button className="btn-outline">
          Nuevo cliente
        </button>
        <button className="btn-outline">
          Importar
        </button>
      </div>

      {/* Tabla de facturas */}
      <DataTable
        columns={columns}
        data={invoices}
        loading={loading}
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={onPageChange}
        onRowDoubleClick={onInvoiceDoubleClick}
        emptyMessage="Facturas adeudadas por el cliente (doble click para seleccionar ítem)"
        stickyHeader={true}
      />
    </div>
  );
};