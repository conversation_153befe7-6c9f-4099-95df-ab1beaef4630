import React, { useState } from 'react';
import { CustomerDetail, Contact } from '../../types/customer.types';
import { FiscalData } from './FiscalData';
import { CommercialData } from './CommercialData';

interface GeneralInfoProps {
  customer: CustomerDetail | null;
  onSave: (data: Partial<CustomerDetail>) => void;
  onDianConsult: (nit: string) => void;
  onAddContact: (contact: Omit<Contact, 'id'>) => void;
}

export const GeneralInfo: React.FC<GeneralInfoProps> = ({
  customer,
  onSave,
  onDianConsult,
  onAddContact,
}) => {
  const [activeSubTab, setActiveSubTab] = useState('fiscal');

  if (!customer) {
    return (
      <div className="text-center py-8 text-gray-500">
        Seleccionar cliente de la lista
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Sub-tabs para datos fiscales y comerciales */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            className={`py-2 px-1 text-sm font-medium ${
              activeSubTab === 'fiscal'
                ? 'border-b-2 border-primary-500 text-primary-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveSubTab('fiscal')}
          >
            Datos fiscales
          </button>
          <button
            className={`py-2 px-1 text-sm font-medium ${
              activeSubTab === 'commercial'
                ? 'border-b-2 border-primary-500 text-primary-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveSubTab('commercial')}
          >
            Datos comerciales
          </button>
        </nav>
      </div>

      {/* Contenido de los sub-tabs */}
      {activeSubTab === 'fiscal' && (
        <FiscalData
          customer={customer}
          onSave={onSave}
          onDianConsult={onDianConsult}
          onAddContact={onAddContact}
        />
      )}

      {activeSubTab === 'commercial' && (
        <CommercialData
          customer={customer}
          onSave={onSave}
        />
      )}
    </div>
  );
};