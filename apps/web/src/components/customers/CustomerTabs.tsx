import React from 'react';
import { Customer, CustomerDetail, OutstandingInvoice, CustomerMovement, Contact } from '../../types/customer.types';
import { OutstandingInvoices } from './OutstandingInvoices';
import { CustomerAccount } from './CustomerAccount';
import { GeneralInfo } from './GeneralInfo';

interface CustomerTabsProps {
  customer: Customer | null;
  customerDetail: CustomerDetail | null;
  activeTab: string;
  outstandingInvoices: OutstandingInvoice[];
  customerMovements: CustomerMovement[];
  loading: boolean;
  currentPage: number;
  totalPages: number;
  onTabChange: (tab: string) => void;
  onPageChange: (page: number, tab: string) => void;
  onInvoiceDoubleClick: (invoice: OutstandingInvoice) => void;
  onMovementDoubleClick: (movement: CustomerMovement) => void;
  onAddInvoice: () => void;
  onAddReceipt: () => void;
  onSaveCustomer: (data: Partial<CustomerDetail>) => void;
  onDianConsult: (nit: string) => void;
  onAddContact: (contact: Omit<Contact, 'id'>) => void;
  canAddInvoice: boolean;
  canAddReceipt: boolean;
}

export const CustomerTabs: React.FC<CustomerTabsProps> = ({
  customer,
  customerDetail,
  activeTab,
  outstandingInvoices,
  customerMovements,
  loading,
  currentPage,
  totalPages,
  onTabChange,
  onPageChange,
  onInvoiceDoubleClick,
  onMovementDoubleClick,
  onAddInvoice,
  onAddReceipt,
  onSaveCustomer,
  onDianConsult,
  onAddContact,
  canAddInvoice,
  canAddReceipt,
}) => {
  const tabs = [
    { id: 'outstanding-invoices', label: 'Facturas adeudadas' },
    { id: 'customer-account', label: 'Cuenta cliente' },
    { id: 'general-info', label: 'Información general' },
    { id: 'other-info', label: 'Otra información' },
  ];

  if (!customer) {
    return (
      <div className="text-center py-8 text-gray-500">
        Seleccionar cliente de la lista
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Tabs navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              className={`py-2 px-1 text-sm font-medium ${
                activeTab === tab.id
                  ? 'border-b-2 border-primary-500 text-primary-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => onTabChange(tab.id)}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab content */}
      <div className="min-h-96">
        {activeTab === 'outstanding-invoices' && (
          <OutstandingInvoices
            customerId={customer.id}
            invoices={outstandingInvoices}
            loading={loading}
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={(page) => onPageChange(page, 'outstanding-invoices')}
            onInvoiceDoubleClick={onInvoiceDoubleClick}
            onAddInvoice={onAddInvoice}
            onAddReceipt={onAddReceipt}
            canAddInvoice={canAddInvoice}
            canAddReceipt={canAddReceipt}
          />
        )}

        {activeTab === 'customer-account' && (
          <CustomerAccount
            customerId={customer.id}
            movements={customerMovements}
            loading={loading}
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={(page) => onPageChange(page, 'customer-account')}
            onMovementDoubleClick={onMovementDoubleClick}
          />
        )}

        {activeTab === 'general-info' && (
          <GeneralInfo
            customer={customerDetail}
            onSave={onSaveCustomer}
            onDianConsult={onDianConsult}
            onAddContact={onAddContact}
          />
        )}

        {activeTab === 'other-info' && (
          <div className="text-center py-8 text-gray-500">
            Otra información del cliente
          </div>
        )}
      </div>
    </div>
  );
};