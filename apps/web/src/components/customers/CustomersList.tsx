import React from 'react';
import { Search } from 'lucide-react';
import { Customer } from '../../types/customer.types';
import { DataTable } from './DataTable';

interface CustomersListProps {
  customers: Customer[];
  selectedCustomer: Customer | null;
  activeFilter: 'active' | 'inactive';
  searchTerm: string;
  loading: boolean;
  currentPage: number;
  totalPages: number;
  onCustomerSelect: (customer: Customer) => void;
  onCustomerDoubleClick: (customer: Customer) => void;
  onActiveFilterChange: (filter: 'active' | 'inactive') => void;
  onSearchChange: (term: string) => void;
  onPageChange: (page: number) => void;
}

export const CustomersList: React.FC<CustomersListProps> = ({
  customers,
  selectedCustomer,
  activeFilter,
  searchTerm,
  loading,
  currentPage,
  totalPages,
  onCustomerSelect,
  onCustomerDoubleClick,
  onActiveFilterChange,
  onSearchChange,
  onPageChange,
}) => {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0,
    }).format(value);
  };

  const columns = [
    {
      key: 'name',
      label: 'Nombre',
      sortable: true,
      render: (value: string, row: Customer) => (
        <div>
          <div className="font-medium text-gray-900">{value}</div>
          <div className="text-sm text-gray-500">{row.nit || row.code}</div>
          <div className="text-sm font-medium text-gray-900">
            {formatCurrency(row.creditLimit)}
          </div>
        </div>
      ),
    },
    {
      key: 'tradeName',
      label: 'Razón social',
      sortable: true,
      render: (value: string, row: Customer) => (
        <div>
          <div className="text-sm text-gray-900">{value || row.name}</div>
        </div>
      ),
    },
    {
      key: 'creditLimit',
      label: 'Saldo',
      sortable: true,
      render: (value: number) => (
        <div className="text-right font-medium">
          {formatCurrency(value)}
        </div>
      ),
    },
  ];

  return (
    <div className="card h-full">
      <div className="card-header">
        <h3 className="text-lg font-medium">Lista de clientes</h3>
        
        {/* Filtros Activos/Inactivos */}
        <div className="flex space-x-4 text-sm font-medium text-gray-500 border-b pb-2 mt-4">
          <button
            className={`pb-2 ${
              activeFilter === 'active'
                ? 'text-primary-600 border-b-2 border-primary-600'
                : 'hover:text-gray-700'
            }`}
            onClick={() => onActiveFilterChange('active')}
          >
            Activos
          </button>
          <button
            className={`pb-2 ${
              activeFilter === 'inactive'
                ? 'text-primary-600 border-b-2 border-primary-600'
                : 'hover:text-gray-700'
            }`}
            onClick={() => onActiveFilterChange('inactive')}
          >
            Inactivos
          </button>
        </div>

        {/* Búsqueda */}
        <div className="mt-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Buscar cliente:
          </label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder=""
              className="input pl-10 w-full"
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
            />
          </div>
        </div>
      </div>

      <div className="card-content flex-1 overflow-hidden">
        <DataTable
          columns={columns}
          data={customers}
          loading={loading}
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={onPageChange}
          onRowClick={onCustomerSelect}
          onRowDoubleClick={onCustomerDoubleClick}
          selectedRowId={selectedCustomer?.id}
          emptyMessage="Sin datos para mostrar"
          stickyHeader={false}
        />
      </div>
    </div>
  );
};