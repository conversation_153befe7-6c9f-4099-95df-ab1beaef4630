import React, { useState } from 'react';
import { CustomerDetail, Address } from '../../types/customer.types';

interface CommercialDataProps {
  customer: CustomerDetail | null;
  onSave: (data: Partial<CustomerDetail>) => void;
}

export const CommercialData: React.FC<CommercialDataProps> = ({
  customer,
  onSave,
}) => {
  const [formData, setFormData] = useState<Partial<CustomerDetail>>(customer || {});
  const [shippingAddress, setShippingAddress] = useState<Address>(
    customer?.shippingAddress || {
      address: '',
      city: '',
      postalCode: '',
      state: '',
      country: 'CO',
    }
  );

  const handleInputChange = (field: keyof CustomerDetail, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleShippingAddressChange = (field: keyof Address, value: string) => {
    setShippingAddress(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = () => {
    onSave({
      ...formData,
      shippingAddress,
    });
  };

  const provinces = [
    'Antioquia', 'Atlántico', 'Bogotá D.C.', 'Bolívar', 'Boyacá', 'Caldas',
    'Caquetá', 'Cauca', 'César', 'Córdoba', 'Cundinamarca', 'Chocó',
    'Huila', 'La Guajira', 'Magdalena', 'Meta', 'Nariño', 'Norte de Santander',
    'Quindío', 'Risaralda', 'Santander', 'Sucre', 'Tolima', 'Valle del Cauca'
  ];

  const countries = [
    { value: 'CO', label: 'Colombia' },
    { value: 'US', label: 'Estados Unidos' },
    { value: 'MX', label: 'México' },
    { value: 'AR', label: 'Argentina' },
    { value: 'BR', label: 'Brasil' },
  ];

  const saleConditions = [
    { value: 'Contado', label: 'Contado' },
    { value: 'Credito', label: 'Crédito' },
  ];

  const priceLists = [
    'Seleccionar lista de precios',
    'Lista General',
    'Lista Mayorista',
    'Lista Especial',
  ];

  const transporters = [
    'Seleccionar proveedor',
    'Transportes ABC',
    'Logística XYZ',
    'Envíos Rápidos',
  ];

  if (!customer) {
    return (
      <div className="text-center py-8 text-gray-500">
        Seleccionar cliente de la lista
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Datos comerciales principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Activo */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Activo
          </label>
          <select
            className="input"
            value={formData.active ? 'Si' : 'No'}
            onChange={(e) => handleInputChange('active', e.target.value === 'Si')}
          >
            <option value="Si">Si</option>
            <option value="No">No</option>
          </select>
        </div>

        {/* Fecha alta */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Fecha alta
          </label>
          <input
            type="date"
            className="input"
            value={formData.createdAt ? new Date(formData.createdAt).toISOString().split('T')[0] : ''}
            onChange={(e) => handleInputChange('createdAt', e.target.value)}
          />
        </div>

        {/* Dirección fiscal */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Dirección fiscal
          </label>
          <input
            type="text"
            className="input"
            value={formData.address || ''}
            onChange={(e) => handleInputChange('address', e.target.value)}
          />
        </div>

        {/* Ciudad */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Ciudad
          </label>
          <input
            type="text"
            className="input"
            value={formData.city || ''}
            onChange={(e) => handleInputChange('city', e.target.value)}
          />
        </div>

        {/* Código Postal */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Cod. Postal
          </label>
          <input
            type="text"
            className="input"
            value={formData.state || ''}
            onChange={(e) => handleInputChange('state', e.target.value)}
          />
        </div>

        {/* Provincia */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Provincia
          </label>
          <select
            className="input"
            value={formData.state || ''}
            onChange={(e) => handleInputChange('state', e.target.value)}
          >
            <option value="">Seleccionar provincia</option>
            {provinces.map(province => (
              <option key={province} value={province}>
                {province}
              </option>
            ))}
          </select>
        </div>

        {/* País */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            País
          </label>
          <select
            className="input"
            value={formData.country || 'CO'}
            onChange={(e) => handleInputChange('country', e.target.value)}
          >
            {countries.map(country => (
              <option key={country.value} value={country.value}>
                {country.label}
              </option>
            ))}
          </select>
        </div>

        {/* Condición de venta */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Condición de venta
          </label>
          <select
            className="input"
            value={formData.saleCondition || 'Contado'}
            onChange={(e) => handleInputChange('saleCondition', e.target.value)}
          >
            {saleConditions.map(condition => (
              <option key={condition.value} value={condition.value}>
                {condition.label}
              </option>
            ))}
          </select>
        </div>

        {/* % Desc */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            % Desc
          </label>
          <input
            type="number"
            min="0"
            max="100"
            step="0.01"
            className="input"
            value={formData.discountPercentage || 0}
            onChange={(e) => handleInputChange('discountPercentage', parseFloat(e.target.value))}
          />
        </div>

        {/* % IVA */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            % IVA
          </label>
          <input
            type="number"
            min="0"
            max="100"
            step="0.01"
            className="input"
            value={formData.ivaPercentage || 0}
            onChange={(e) => handleInputChange('ivaPercentage', parseFloat(e.target.value))}
          />
        </div>

        {/* Cuenta de ingresos */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Cuenta de ingresos
          </label>
          <select
            className="input"
            value={formData.incomeAccount || ''}
            onChange={(e) => handleInputChange('incomeAccount', e.target.value)}
          >
            <option value="">Seleccionar cuenta</option>
            <option value="4135">4135 - Ingresos por servicios</option>
            <option value="4140">4140 - Ingresos por ventas</option>
          </select>
        </div>

        {/* Lista de precios */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Lista de precios
          </label>
          <select
            className="input"
            value={formData.priceList || ''}
            onChange={(e) => handleInputChange('priceList', e.target.value)}
          >
            {priceLists.map(list => (
              <option key={list} value={list}>
                {list}
              </option>
            ))}
          </select>
        </div>

        {/* Transportista */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Transportista
          </label>
          <select
            className="input"
            value={formData.transporter || ''}
            onChange={(e) => handleInputChange('transporter', e.target.value)}
          >
            {transporters.map(transporter => (
              <option key={transporter} value={transporter}>
                {transporter}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Dirección para envíos */}
      <div className="border-t pt-6">
        <h4 className="text-lg font-medium text-gray-900 mb-4">Dirección para envíos</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Dirección de envío */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Dirección
            </label>
            <input
              type="text"
              className="input"
              value={shippingAddress.address}
              onChange={(e) => handleShippingAddressChange('address', e.target.value)}
            />
          </div>

          {/* Ciudad de envío */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Ciudad
            </label>
            <input
              type="text"
              className="input"
              value={shippingAddress.city}
              onChange={(e) => handleShippingAddressChange('city', e.target.value)}
            />
          </div>

          {/* Código Postal de envío */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Cod. Postal
            </label>
            <input
              type="text"
              className="input"
              value={shippingAddress.postalCode}
              onChange={(e) => handleShippingAddressChange('postalCode', e.target.value)}
            />
          </div>

          {/* Provincia de envío */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Provincia
            </label>
            <select
              className="input"
              value={shippingAddress.state}
              onChange={(e) => handleShippingAddressChange('state', e.target.value)}
            >
              <option value="">Seleccionar provincia</option>
              {provinces.map(province => (
                <option key={province} value={province}>
                  {province}
                </option>
              ))}
            </select>
          </div>

          {/* País de envío */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              País
            </label>
            <select
              className="input"
              value={shippingAddress.country}
              onChange={(e) => handleShippingAddressChange('country', e.target.value)}
            >
              {countries.map(country => (
                <option key={country.value} value={country.value}>
                  {country.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Botón guardar */}
      <div className="flex justify-end">
        <button onClick={handleSave} className="btn-primary">
          Guardar
        </button>
      </div>
    </div>
  );
};