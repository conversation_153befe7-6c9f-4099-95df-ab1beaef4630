import React, { useState, useEffect } from 'react';
import { X, Paperclip } from 'lucide-react';
import { CreateInvoiceData, InvoiceItem } from '../../types/invoice.types';
import { Customer } from '../../types/customer.types';

interface InvoiceModalProps {
  isOpen: boolean;
  customer: Customer;
  onClose: () => void;
  onSave: (invoice: CreateInvoiceData) => void;
}

export const InvoiceModal: React.FC<InvoiceModalProps> = ({
  isOpen,
  customer,
  onClose,
  onSave,
}) => {
  const [formData, setFormData] = useState<CreateInvoiceData>({
    fechaFactura: new Date(),
    fechaVencimiento: new Date(),
    condicionVenta: 'Contado',
    tipoComprobante: 'Fa',
    tipoFactura: 'A',
    estado: 'Aprobada',
    numeroFactura: '',
    descripcion: '',
    moneda: 'Peso Argentino',
    listaPrecio: 'Seleccionar lista de precios',
    generarRemito: false,
    items: [],
    netoGravado: 0,
    netoNoGravado: 0,
    totalIVA: 0,
    percepcionIVA: 0,
    percepcionICA: 0,
    totalFactura: 0,
    attachments: [],
  });

  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  // Inicializar con algunos items vacíos
  useEffect(() => {
    if (isOpen && formData.items.length === 0) {
      addEmptyLines(5);
    }
  }, [isOpen, formData.items.length]);

  const addEmptyLines = (count: number) => {
    const newItems: InvoiceItem[] = [];
    for (let i = 0; i < count; i++) {
      newItems.push({
        id: `temp-${Date.now()}-${i}`,
        itemCode: '',
        description: '',
        warehouse: 'FLETE',
        freight: 0,
        salespeople: [],
        unit: 'Un',
        quantity: 0,
        unitPrice: 0,
        discountPercentage: 0,
        ivaPercentage: 0,
        subtotal: 0,
        account: 'Mercaderías',
      });
    }
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, ...newItems],
    }));
  };

  const updateItem = (index: number, field: keyof InvoiceItem, value: any) => {
    const updatedItems = [...formData.items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    
    // Recalcular subtotal del item
    if (['quantity', 'unitPrice', 'discountPercentage', 'ivaPercentage'].includes(field)) {
      const item = updatedItems[index];
      const baseAmount = item.quantity * item.unitPrice;
      const discountAmount = baseAmount * (item.discountPercentage / 100);
      const netAmount = baseAmount - discountAmount;
      const ivaAmount = netAmount * (item.ivaPercentage / 100);
      updatedItems[index].subtotal = netAmount + ivaAmount;
    }

    setFormData(prev => ({ ...prev, items: updatedItems }));
    calculateTotals(updatedItems);
  };

  const calculateTotals = (items: InvoiceItem[]) => {
    let netoGravado = 0;
    let netoNoGravado = 0;
    let totalIVA = 0;

    items.forEach(item => {
      if (item.quantity > 0 && item.unitPrice > 0) {
        const baseAmount = item.quantity * item.unitPrice;
        const discountAmount = baseAmount * (item.discountPercentage / 100);
        const netAmount = baseAmount - discountAmount;
        
        if (item.ivaPercentage > 0) {
          netoGravado += netAmount;
          totalIVA += netAmount * (item.ivaPercentage / 100);
        } else {
          netoNoGravado += netAmount;
        }
      }
    });

    const percepcionIVA = netoGravado * 0.021; // 2.1% ejemplo
    const percepcionICA = netoGravado * 0.007; // 0.7% ejemplo
    const totalFactura = netoGravado + netoNoGravado + totalIVA + percepcionIVA + percepcionICA;

    setFormData(prev => ({
      ...prev,
      netoGravado,
      netoNoGravado,
      totalIVA,
      percepcionIVA,
      percepcionICA,
      totalFactura,
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (formData.fechaVencimiento < formData.fechaFactura) {
      newErrors.fechaVencimiento = 'Fecha vencimiento debe ser mayor o igual a fecha factura';
    }

    if (!formData.numeroFactura.trim()) {
      newErrors.numeroFactura = 'Número de factura es requerido';
    }

    const validItems = formData.items.filter(item => item.quantity > 0);
    if (validItems.length === 0) {
      newErrors.items = 'Debe agregar al menos un ítem';
    }

    // Validar items
    formData.items.forEach((item, index) => {
      if (item.quantity > 0) {
        if (item.unitPrice < 0) {
          newErrors[`item-${index}-unitPrice`] = 'Precio unitario debe ser mayor o igual a 0';
        }
        if (item.discountPercentage < 0 || item.discountPercentage > 100) {
          newErrors[`item-${index}-discount`] = 'Descuento debe estar entre 0 y 100%';
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (validateForm()) {
      onSave(formData);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setFormData(prev => ({
      ...prev,
      attachments: [...prev.attachments, ...files],
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold">
            Ingresar factura para {customer.name}
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
          {/* Encabezado de factura */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Fecha factura
              </label>
              <input
                type="date"
                className="input"
                value={formData.fechaFactura.toISOString().split('T')[0]}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  fechaFactura: new Date(e.target.value)
                }))}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Fecha vencimiento
              </label>
              <input
                type="date"
                className={`input ${errors.fechaVencimiento ? 'border-red-500' : ''}`}
                value={formData.fechaVencimiento.toISOString().split('T')[0]}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  fechaVencimiento: new Date(e.target.value)
                }))}
              />
              {errors.fechaVencimiento && (
                <p className="text-red-500 text-sm mt-1">{errors.fechaVencimiento}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Condición de venta
              </label>
              <select
                className="input"
                value={formData.condicionVenta}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  condicionVenta: e.target.value as 'Contado' | 'Credito'
                }))}
              >
                <option value="Contado">Contado</option>
                <option value="Credito">Crédito</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tipo comprobante
              </label>
              <select
                className="input"
                value={formData.tipoComprobante}
                onChange={(e) => setFormData(prev => ({ ...prev, tipoComprobante: e.target.value }))}
              >
                <option value="Fa">Fa</option>
                <option value="NC">NC</option>
                <option value="ND">ND</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tipo factura
              </label>
              <select
                className="input"
                value={formData.tipoFactura}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  tipoFactura: e.target.value as 'A' | 'B' | 'C'
                }))}
              >
                <option value="A">A</option>
                <option value="B">B</option>
                <option value="C">C</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Estado
              </label>
              <select
                className="input"
                value={formData.estado}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  estado: e.target.value as 'Borrador' | 'Aprobada'
                }))}
              >
                <option value="Borrador">Borrador</option>
                <option value="Aprobada">Aprobada</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nro. factura
              </label>
              <input
                type="text"
                className={`input ${errors.numeroFactura ? 'border-red-500' : ''}`}
                value={formData.numeroFactura}
                onChange={(e) => setFormData(prev => ({ ...prev, numeroFactura: e.target.value }))}
                placeholder="-"
              />
              {errors.numeroFactura && (
                <p className="text-red-500 text-sm mt-1">{errors.numeroFactura}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Descripción
              </label>
              <input
                type="text"
                className="input"
                value={formData.descripcion}
                onChange={(e) => setFormData(prev => ({ ...prev, descripcion: e.target.value }))}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Moneda
              </label>
              <select
                className="input"
                value={formData.moneda}
                onChange={(e) => setFormData(prev => ({ ...prev, moneda: e.target.value }))}
              >
                <option value="Peso Argentino">Peso Argentino</option>
                <option value="Dólar">Dólar</option>
                <option value="Euro">Euro</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Lista de precios
              </label>
              <select
                className="input"
                value={formData.listaPrecio}
                onChange={(e) => setFormData(prev => ({ ...prev, listaPrecio: e.target.value }))}
              >
                <option value="Seleccionar lista de precios">Seleccionar lista de precios</option>
                <option value="Lista General">Lista General</option>
                <option value="Lista Mayorista">Lista Mayorista</option>
              </select>
            </div>
          </div>

          {/* Checkbox y enlaces */}
          <div className="flex items-center space-x-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.generarRemito}
                onChange={(e) => setFormData(prev => ({ ...prev, generarRemito: e.target.checked }))}
                className="mr-2"
              />
              Generar remito automático
            </label>
            <button className="text-blue-600 hover:underline text-sm">
              Datos adicionales del comprobante
            </button>
          </div>

          {/* Grilla de ítems */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Ítems de factura</h3>
              <div className="space-x-2">
                <button
                  onClick={() => addEmptyLines(5)}
                  className="btn-outline"
                >
                  Agregar 5 líneas
                </button>
                <button className="btn-outline">
                  Remitos no facturados de este cliente
                </button>
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full border border-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Código</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Descripción</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Depósito</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">FLETE</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Vendedores</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">UM</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Cant</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">P Unit</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">%Desc</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">%IVA</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Subtotal</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Cuenta</th>
                  </tr>
                </thead>
                <tbody>
                  {formData.items.map((item, index) => (
                    <tr key={item.id} className="border-t">
                      <td className="px-3 py-2">
                        <input
                          type="text"
                          className="w-full px-2 py-1 text-sm border rounded"
                          value={item.itemCode}
                          onChange={(e) => updateItem(index, 'itemCode', e.target.value)}
                        />
                      </td>
                      <td className="px-3 py-2">
                        <input
                          type="text"
                          className="w-full px-2 py-1 text-sm border rounded"
                          value={item.description}
                          onChange={(e) => updateItem(index, 'description', e.target.value)}
                        />
                      </td>
                      <td className="px-3 py-2">
                        <select
                          className="w-full px-2 py-1 text-sm border rounded"
                          value={item.warehouse}
                          onChange={(e) => updateItem(index, 'warehouse', e.target.value)}
                        >
                          <option value="FLETE">FLETE</option>
                          <option value="PRINCIPAL">PRINCIPAL</option>
                        </select>
                      </td>
                      <td className="px-3 py-2">
                        <input
                          type="number"
                          className="w-full px-2 py-1 text-sm border rounded"
                          value={item.freight}
                          onChange={(e) => updateItem(index, 'freight', parseFloat(e.target.value) || 0)}
                        />
                      </td>
                      <td className="px-3 py-2">
                        <input
                          type="text"
                          className="w-full px-2 py-1 text-sm border rounded"
                          placeholder="Vendedores"
                        />
                      </td>
                      <td className="px-3 py-2">
                        <select
                          className="w-full px-2 py-1 text-sm border rounded"
                          value={item.unit}
                          onChange={(e) => updateItem(index, 'unit', e.target.value)}
                        >
                          <option value="Un">Un</option>
                          <option value="Kg">Kg</option>
                          <option value="Mt">Mt</option>
                        </select>
                      </td>
                      <td className="px-3 py-2">
                        <input
                          type="number"
                          step="0.01"
                          className="w-full px-2 py-1 text-sm border rounded"
                          value={item.quantity}
                          onChange={(e) => updateItem(index, 'quantity', parseFloat(e.target.value) || 0)}
                        />
                      </td>
                      <td className="px-3 py-2">
                        <input
                          type="number"
                          step="0.01"
                          className="w-full px-2 py-1 text-sm border rounded"
                          value={item.unitPrice}
                          onChange={(e) => updateItem(index, 'unitPrice', parseFloat(e.target.value) || 0)}
                        />
                      </td>
                      <td className="px-3 py-2">
                        <input
                          type="number"
                          min="0"
                          max="100"
                          step="0.01"
                          className="w-full px-2 py-1 text-sm border rounded"
                          value={item.discountPercentage}
                          onChange={(e) => updateItem(index, 'discountPercentage', parseFloat(e.target.value) || 0)}
                        />
                      </td>
                      <td className="px-3 py-2">
                        <input
                          type="number"
                          min="0"
                          max="100"
                          step="0.01"
                          className="w-full px-2 py-1 text-sm border rounded"
                          value={item.ivaPercentage}
                          onChange={(e) => updateItem(index, 'ivaPercentage', parseFloat(e.target.value) || 0)}
                        />
                      </td>
                      <td className="px-3 py-2">
                        <span className="text-sm">
                          {new Intl.NumberFormat('es-CO', {
                            style: 'currency',
                            currency: 'COP',
                          }).format(item.subtotal)}
                        </span>
                      </td>
                      <td className="px-3 py-2">
                        <select
                          className="w-full px-2 py-1 text-sm border rounded"
                          value={item.account}
                          onChange={(e) => updateItem(index, 'account', e.target.value)}
                        >
                          <option value="Mercaderías">Mercaderías</option>
                          <option value="Servicios">Servicios</option>
                        </select>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Totales */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <div className="flex justify-between">
                <span>Neto gravado ARS:</span>
                <span>{new Intl.NumberFormat('es-CO', { style: 'currency', currency: 'COP' }).format(formData.netoGravado)}</span>
              </div>
              <div className="flex justify-between">
                <span>Neto no gravado ARS:</span>
                <span>{new Intl.NumberFormat('es-CO', { style: 'currency', currency: 'COP' }).format(formData.netoNoGravado)}</span>
              </div>
              <div className="flex justify-between">
                <span>Total IVA:</span>
                <span>{new Intl.NumberFormat('es-CO', { style: 'currency', currency: 'COP' }).format(formData.totalIVA)}</span>
              </div>
              <div className="flex justify-between">
                <span>Percepción IVA ARS:</span>
                <span>{new Intl.NumberFormat('es-CO', { style: 'currency', currency: 'COP' }).format(formData.percepcionIVA)}</span>
              </div>
              <div className="flex justify-between">
                <span>Percepción IIBB ARS:</span>
                <span>{new Intl.NumberFormat('es-CO', { style: 'currency', currency: 'COP' }).format(formData.percepcionICA)}</span>
              </div>
              <div className="flex justify-between font-bold text-lg border-t pt-2">
                <span>Total factura ARS:</span>
                <span>{new Intl.NumberFormat('es-CO', { style: 'currency', currency: 'COP' }).format(formData.totalFactura)}</span>
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">Adjuntos</h4>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                <input
                  type="file"
                  multiple
                  onChange={handleFileUpload}
                  className="hidden"
                  id="file-upload"
                />
                <label
                  htmlFor="file-upload"
                  className="flex items-center justify-center cursor-pointer"
                >
                  <Paperclip className="w-5 h-5 mr-2" />
                  Adjuntar archivo
                </label>
                {formData.attachments.length > 0 && (
                  <div className="mt-2 space-y-1">
                    {formData.attachments.map((file, index) => (
                      <div key={index} className="text-sm text-gray-600">
                        {file.name}
                      </div>
                    ))}
                  </div>
                )}
              </div>
              <button className="btn-outline mt-2 w-full">
                Adjuntar archivo
              </button>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-2 p-6 border-t bg-gray-50">
          <button onClick={onClose} className="btn-outline">
            Cancelar
          </button>
          <button onClick={handleSave} className="btn-primary">
            Guardar
          </button>
          <button onClick={handleSave} className="btn-primary">
            Guardar e imprimir
          </button>
        </div>
      </div>
    </div>
  );
};