import React, { useState } from 'react';
import { CustomerDetail, Contact, TaxResponsibility } from '../../types/customer.types';

interface FiscalDataProps {
  customer: CustomerDetail | null;
  onSave: (data: Partial<CustomerDetail>) => void;
  onDianConsult: (nit: string) => void;
  onAddContact: (contact: Omit<Contact, 'id'>) => void;
}

export const FiscalData: React.FC<FiscalDataProps> = ({
  customer,
  onSave,
  onDianConsult,
  onAddContact,
}) => {
  const [formData, setFormData] = useState<Partial<CustomerDetail>>(customer || {});
  const [activeSubTab, setActiveSubTab] = useState('contactos');
  const [showContactModal, setShowContactModal] = useState(false);
  const [newContact, setNewContact] = useState<Omit<Contact, 'id'>>({
    name: '',
    position: '',
    email: '',
    phone: '',
    notes: '',
  });

  const handleInputChange = (field: keyof CustomerDetail, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = () => {
    onSave(formData);
  };

  const handleDianConsult = () => {
    if (formData.nit) {
      onDianConsult(formData.nit);
    }
  };

  const handleAddContact = () => {
    onAddContact(newContact);
    setNewContact({
      name: '',
      position: '',
      email: '',
      phone: '',
      notes: '',
    });
    setShowContactModal(false);
  };

  const taxResponsibilityOptions = [
    { value: TaxResponsibility.RESPONSABLE_IVA, label: 'Responsable de IVA' },
    { value: TaxResponsibility.NO_RESPONSABLE_IVA, label: 'No responsable de IVA' },
    { value: TaxResponsibility.GRAN_CONTRIBUYENTE, label: 'Gran contribuyente' },
    { value: TaxResponsibility.REGIMEN_SIMPLE, label: 'Régimen simple' },
    { value: TaxResponsibility.PERSONA_NATURAL, label: 'Persona natural' },
  ];

  const provinces = [
    'Antioquia', 'Atlántico', 'Bogotá D.C.', 'Bolívar', 'Boyacá', 'Caldas',
    'Caquetá', 'Cauca', 'César', 'Córdoba', 'Cundinamarca', 'Chocó',
    'Huila', 'La Guajira', 'Magdalena', 'Meta', 'Nariño', 'Norte de Santander',
    'Quindío', 'Risaralda', 'Santander', 'Sucre', 'Tolima', 'Valle del Cauca'
  ];

  const countries = [
    { value: 'CO', label: 'Colombia' },
    { value: 'US', label: 'Estados Unidos' },
    { value: 'MX', label: 'México' },
    { value: 'AR', label: 'Argentina' },
    { value: 'BR', label: 'Brasil' },
  ];

  if (!customer) {
    return (
      <div className="text-center py-8 text-gray-500">
        Seleccionar cliente de la lista
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Formulario de datos fiscales */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* NIT con botón consultar DIAN */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            NIT
          </label>
          <div className="flex space-x-2">
            <input
              type="text"
              className="input flex-1"
              value={formData.nit || ''}
              onChange={(e) => handleInputChange('nit', e.target.value)}
              placeholder="30.695.366-8"
            />
            <button
              onClick={handleDianConsult}
              className="btn-outline whitespace-nowrap"
            >
              Consultar en DIAN
            </button>
          </div>
        </div>

        {/* Razón social */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Razón social
          </label>
          <input
            type="text"
            className="input"
            value={formData.tradeName || ''}
            onChange={(e) => handleInputChange('tradeName', e.target.value)}
            placeholder="A E B ARGENTINA SA"
          />
        </div>

        {/* Nombre */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Nombre
          </label>
          <input
            type="text"
            className="input"
            value={formData.name || ''}
            onChange={(e) => handleInputChange('name', e.target.value)}
          />
        </div>

        {/* Condición IVA */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Condición IVA/Resp. fiscal
          </label>
          <select
            className="input"
            value={formData.taxResponsibility || ''}
            onChange={(e) => handleInputChange('taxResponsibility', e.target.value)}
          >
            {taxResponsibilityOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Dirección */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Dirección
          </label>
          <input
            type="text"
            className="input"
            value={formData.address || ''}
            onChange={(e) => handleInputChange('address', e.target.value)}
            placeholder="CARRIL RODRIGUEZ PEÑA 200-A 4094"
          />
        </div>

        {/* Ciudad */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Ciudad
          </label>
          <input
            type="text"
            className="input"
            value={formData.city || ''}
            onChange={(e) => handleInputChange('city', e.target.value)}
            placeholder="COQUIMBO"
          />
        </div>

        {/* Código Postal */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Cod. Postal
          </label>
          <input
            type="text"
            className="input"
            value={formData.state || ''}
            onChange={(e) => handleInputChange('state', e.target.value)}
            placeholder="5513"
          />
        </div>

        {/* Provincia */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Provincia
          </label>
          <select
            className="input"
            value={formData.state || ''}
            onChange={(e) => handleInputChange('state', e.target.value)}
          >
            <option value="">Seleccionar provincia</option>
            {provinces.map(province => (
              <option key={province} value={province}>
                {province}
              </option>
            ))}
          </select>
        </div>

        {/* País */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            País
          </label>
          <select
            className="input"
            value={formData.country || 'CO'}
            onChange={(e) => handleInputChange('country', e.target.value)}
          >
            {countries.map(country => (
              <option key={country.value} value={country.value}>
                {country.label}
              </option>
            ))}
          </select>
        </div>

        {/* Teléfono */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Teléfono
          </label>
          <input
            type="text"
            className="input"
            value={formData.phone || ''}
            onChange={(e) => handleInputChange('phone', e.target.value)}
          />
        </div>

        {/* DNI/Pasaporte */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            DNI/Pasaporte
          </label>
          <input
            type="text"
            className="input"
            value={formData.nit || ''}
            onChange={(e) => handleInputChange('nit', e.target.value)}
          />
        </div>

        {/* Email */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Email
          </label>
          <input
            type="email"
            className="input"
            value={formData.email || ''}
            onChange={(e) => handleInputChange('email', e.target.value)}
          />
        </div>
      </div>

      {/* Tabs secundarios */}
      <div className="border-t pt-6">
        <div className="border-b border-gray-200 mb-4">
          <nav className="-mb-px flex space-x-8">
            {['contactos', 'comentarios', 'historia', 'facturas-recurrentes'].map((tab) => (
              <button
                key={tab}
                className={`py-2 px-1 text-sm font-medium ${
                  activeSubTab === tab
                    ? 'border-b-2 border-primary-500 text-primary-600'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
                onClick={() => setActiveSubTab(tab)}
              >
                {tab.charAt(0).toUpperCase() + tab.slice(1).replace('-', ' ')}
              </button>
            ))}
          </nav>
        </div>

        {/* Contenido de tabs secundarios */}
        {activeSubTab === 'contactos' && (
          <div className="space-y-4">
            <button
              onClick={() => setShowContactModal(true)}
              className="btn-primary"
            >
              Agregar contacto
            </button>
            
            {customer.contacts && customer.contacts.length > 0 ? (
              <div className="space-y-2">
                {customer.contacts.map((contact) => (
                  <div key={contact.id} className="p-4 border rounded-lg">
                    <div className="font-medium">{contact.name}</div>
                    <div className="text-sm text-gray-600">{contact.position}</div>
                    <div className="text-sm text-gray-600">{contact.email}</div>
                    <div className="text-sm text-gray-600">{contact.phone}</div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-gray-500">No hay contactos registrados</div>
            )}
          </div>
        )}

        {activeSubTab === 'comentarios' && (
          <div className="text-gray-500">Sección de comentarios</div>
        )}

        {activeSubTab === 'historia' && (
          <div className="text-gray-500">Historial del cliente</div>
        )}

        {activeSubTab === 'facturas-recurrentes' && (
          <div className="text-gray-500">Facturas recurrentes</div>
        )}
      </div>

      {/* Botón guardar */}
      <div className="flex justify-end">
        <button onClick={handleSave} className="btn-primary">
          Guardar
        </button>
      </div>

      {/* Modal agregar contacto */}
      {showContactModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium mb-4">Agregar contacto</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nombre
                </label>
                <input
                  type="text"
                  className="input"
                  value={newContact.name}
                  onChange={(e) => setNewContact(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Cargo
                </label>
                <input
                  type="text"
                  className="input"
                  value={newContact.position}
                  onChange={(e) => setNewContact(prev => ({ ...prev, position: e.target.value }))}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email
                </label>
                <input
                  type="email"
                  className="input"
                  value={newContact.email}
                  onChange={(e) => setNewContact(prev => ({ ...prev, email: e.target.value }))}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Teléfono
                </label>
                <input
                  type="text"
                  className="input"
                  value={newContact.phone}
                  onChange={(e) => setNewContact(prev => ({ ...prev, phone: e.target.value }))}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Notas
                </label>
                <textarea
                  className="input"
                  rows={3}
                  value={newContact.notes}
                  onChange={(e) => setNewContact(prev => ({ ...prev, notes: e.target.value }))}
                />
              </div>
            </div>

            <div className="flex justify-end space-x-2 mt-6">
              <button
                onClick={() => setShowContactModal(false)}
                className="btn-outline"
              >
                Cancelar
              </button>
              <button
                onClick={handleAddContact}
                className="btn-primary"
              >
                Guardar
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};