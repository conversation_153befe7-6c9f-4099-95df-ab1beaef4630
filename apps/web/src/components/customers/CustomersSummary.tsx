import React, { useState } from 'react';
import { BarChart, Bar, ComposedChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { Calendar, Settings, HelpCircle } from 'lucide-react';

interface CustomersSummaryProps {
  onCalendarClick: () => void;
  onConfigureClick: () => void;
  onHelpClick: () => void;
}

const CustomersSummary: React.FC<CustomersSummaryProps> = ({
  onCalendarClick,
  onConfigureClick,
  onHelpClick,
}) => {
  const [selectedPeriod, setSelectedPeriod] = useState('Mensual');
  const [selectedChartPeriod, setSelectedChartPeriod] = useState('Mensual');

  // Datos de ejemplo para los gráficos
  const invoiceDueData = [
    { name: 'Vencido', value: 35, color: '#10B981' },
    { name: '1 semana', value: 25, color: '#6B7280' },
    { name: '2 semanas', value: 20, color: '#6B7280' },
    { name: '3 semanas', value: 15, color: '#6B7280' },
    { name: '4 semanas', value: 5, color: '#6B7280' },
  ];

  const clientInvoicesData = [
    { month: 'Sep', totalIngresos: 100, acumulado: 100 },
    { month: 'Oct', totalIngresos: 150, acumulado: 250 },
    { month: 'Nov', totalIngresos: 120, acumulado: 370 },
    { month: 'Dic', totalIngresos: 180, acumulado: 550 },
    { month: 'Ene', totalIngresos: 200, acumulado: 750 },
    { month: 'Feb', totalIngresos: 160, acumulado: 910 },
    { month: 'Mar', totalIngresos: 220, acumulado: 1130 },
    { month: 'Abr', totalIngresos: 190, acumulado: 1320 },
    { month: 'May', totalIngresos: 240, acumulado: 1560 },
    { month: 'Jun', totalIngresos: 210, acumulado: 1770 },
    { month: 'Jul', totalIngresos: 180, acumulado: 1950 },
    { month: 'Ago', totalIngresos: 200, acumulado: 2150 },
  ];

  // Datos de ejemplo para la tabla de facturas
  const invoicesData = [
    {
      fecha: '13/06/20...',
      fechaVen: '13/06/20...',
      cliente: 'Client 01',
      nroFactura: '0002-00013...',
      tipo: 'FAV',
      descripcion: 'honorarios',
      valorME: 546.00,
      neto: 57.34,
      iva: 603.34,
      retenciones: 0.00,
      total: 603.34,
      cobrado: 0.00,
      saldo: 603.34,
    },
    {
      fecha: '13/06/20...',
      fechaVen: '13/06/20...',
      cliente: 'Client 01',
      nroFactura: '1111-00000...',
      tipo: 'FAV',
      descripcion: 'honorarios',
      valorME: 80000.00,
      neto: 16800.00,
      iva: 96800.00,
      retenciones: 0.00,
      total: 96800.00,
      cobrado: 0.00,
      saldo: 96800.00,
    },
    {
      fecha: '25/06/20...',
      fechaVen: '25/06/20...',
      cliente: 'Client 01',
      nroFactura: '0001-00000...',
      tipo: 'FVC',
      descripcion: '',
      valorME: 1155.00,
      neto: 0.00,
      iva: 1155.00,
      retenciones: 0.00,
      total: 1155.00,
      cobrado: 0.00,
      saldo: 1155.00,
    },
    {
      fecha: '01/07/20...',
      fechaVen: '01/07/20...',
      cliente: 'Client 01',
      nroFactura: '0001-00000...',
      tipo: 'FAV',
      descripcion: '',
      valorME: 74.53,
      neto: 8.84,
      iva: 83.37,
      retenciones: 0.00,
      total: 83.37,
      cobrado: 0.00,
      saldo: 83.37,
    },
    {
      fecha: '05/08/20...',
      fechaVen: '05/08/20...',
      cliente: 'Client 01',
      nroFactura: '0002-00013...',
      tipo: 'FAV',
      descripcion: '',
      valorME: 99750.00,
      neto: 20947.50,
      iva: 120697.50,
      retenciones: 0.00,
      total: 120697.50,
      cobrado: 0.00,
      saldo: 120697.50,
    },
  ];

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 2,
    }).format(value);
  };

  return (
    <div className="space-y-6">
      {/* Header con botones */}
      <div className="flex items-center justify-between bg-white p-4 rounded-lg shadow-sm">
        <div className="flex items-center space-x-4">
          <button
            onClick={onHelpClick}
            className="flex items-center space-x-2 text-sm text-blue-600 hover:text-blue-800"
          >
            <HelpCircle className="w-4 h-4" />
            <span>¿Querés emitir Factura electrónica?</span>
          </button>
          <button
            onClick={onConfigureClick}
            className="flex items-center space-x-2 px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded"
          >
            <Settings className="w-4 h-4" />
            <span>Configuralo ahora</span>
          </button>
        </div>
        <button
          onClick={onCalendarClick}
          className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
        >
          <Calendar className="w-4 h-4" />
          <span>Calendario de vencimientos</span>
        </button>
      </div>

      {/* Gráficos */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Vencimiento de facturas */}
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
              Vencimiento de facturas
            </h3>
            <div className="flex space-x-1">
              {['Semana', 'Mes', 'Trimestre', 'Año'].map((period) => (
                <button
                  key={period}
                  onClick={() => setSelectedPeriod(period)}
                  className={`px-2 py-1 text-xs rounded ${
                    selectedPeriod === period
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  {period}
                </button>
              ))}
            </div>
          </div>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={invoiceDueData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip formatter={(value) => [`$${value}`, 'Valor']} />
                <Bar dataKey="value" fill="#10B981" />
              </BarChart>
            </ResponsiveContainer>
          </div>
          <div className="mt-4 text-center text-sm text-gray-600">
            Próximas 4 semanas
          </div>
          <div className="flex items-center justify-center space-x-4 mt-2">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded"></div>
              <span className="text-sm">Total ingresos</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-gray-400 rounded"></div>
              <span className="text-sm">Saldo</span>
            </div>
          </div>
        </div>

        {/* Facturas de clientes */}
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
              Facturas de clientes
            </h3>
            <div className="flex space-x-1">
              {['Mensual', 'Trimestral', 'Anual'].map((period) => (
                <button
                  key={period}
                  onClick={() => setSelectedChartPeriod(period)}
                  className={`px-2 py-1 text-xs rounded ${
                    selectedChartPeriod === period
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  {period}
                </button>
              ))}
            </div>
          </div>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <ComposedChart data={clientInvoicesData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => [`$${value}`, 'Valor']} />
                <Bar dataKey="totalIngresos" fill="#10B981" />
                <Line 
                  type="monotone" 
                  dataKey="acumulado" 
                  stroke="#6B7280" 
                  strokeWidth={2}
                  dot={{ fill: '#6B7280', strokeWidth: 2, r: 4 }}
                />
              </ComposedChart>
            </ResponsiveContainer>
          </div>
          <div className="mt-4 text-center text-sm text-gray-600">
            Últimos 12 meses
          </div>
          <div className="flex items-center justify-center space-x-4 mt-2">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded"></div>
              <span className="text-sm">Total ingresos</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-gray-400 rounded"></div>
              <span className="text-sm">Acumulado</span>
            </div>
          </div>
        </div>
      </div>

      {/* Reportes sidebar */}
      <div className="flex gap-6">
        <div className="flex-1">
          {/* Tabla de facturas */}
          <div className="bg-white rounded-lg shadow-sm">
            <div className="px-6 py-4 border-b border-gray-200 flex items-center">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
              <h3 className="text-lg font-medium text-gray-900">Facturas borrador</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Fecha
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Fecha ven.
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Cliente
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Nro Factura
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tipo
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Descripción
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Valor ME
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Neto
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      IVA
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Retenciones
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Cobrado
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Saldo
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {invoicesData.map((invoice, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {invoice.fecha}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {invoice.fechaVen}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {invoice.cliente}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {invoice.nroFactura}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {invoice.tipo}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {invoice.descripcion}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                        {formatCurrency(invoice.valorME)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                        {formatCurrency(invoice.neto)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                        {formatCurrency(invoice.iva)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                        {formatCurrency(invoice.retenciones)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                        {formatCurrency(invoice.total)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                        {formatCurrency(invoice.cobrado)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                        {formatCurrency(invoice.saldo)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <button className="p-1 rounded hover:bg-gray-100">
                  <span className="text-sm">‹‹</span>
                </button>
                <button className="p-1 rounded hover:bg-gray-100">
                  <span className="text-sm">‹</span>
                </button>
                <span className="text-sm text-gray-600">Página 1 de 1</span>
                <button className="p-1 rounded hover:bg-gray-100">
                  <span className="text-sm">›</span>
                </button>
                <button className="p-1 rounded hover:bg-gray-100">
                  <span className="text-sm">››</span>
                </button>
                <button className="p-1 rounded hover:bg-gray-100">
                  <span className="text-sm">⟲</span>
                </button>
              </div>
              <div className="text-sm text-gray-600">
                Mostrando 1 - 5 de 5
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar de reportes */}
        <div className="w-64">
          <div className="bg-white rounded-lg shadow-sm p-4">
            <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
              Reportes
            </h4>
            <div className="space-y-1">
              <button className="w-full text-left text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-50 py-2 px-2 rounded">
                Listado de clientes
              </button>
              <button className="w-full text-left text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-50 py-2 px-2 rounded">
                Listado de facturas
              </button>
              <button className="w-full text-left text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-50 py-2 px-2 rounded">
                Listado de cobranzas
              </button>
              <button className="w-full text-left text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-50 py-2 px-2 rounded">
                IVA Ventas
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomersSummary;