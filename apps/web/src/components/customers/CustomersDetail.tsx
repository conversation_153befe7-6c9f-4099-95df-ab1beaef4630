import React from 'react';
import { Customer, CustomerDetail, OutstandingInvoice, CustomerMovement, Contact } from '../../types/customer.types';
import { CustomerTabs } from './CustomerTabs';

interface CustomersDetailProps {
  customer: Customer | null;
  customerDetail: CustomerDetail | null;
  activeTab: string;
  outstandingInvoices: OutstandingInvoice[];
  customerMovements: CustomerMovement[];
  loading: boolean;
  currentPage: number;
  totalPages: number;
  onTabChange: (tab: string) => void;
  onPageChange: (page: number, tab: string) => void;
  onInvoiceDoubleClick: (invoice: OutstandingInvoice) => void;
  onMovementDoubleClick: (movement: CustomerMovement) => void;
  onAddInvoice: () => void;
  onAddReceipt: () => void;
  onSaveCustomer: (data: Partial<CustomerDetail>) => void;
  onDianConsult: (nit: string) => void;
  onAddContact: (contact: Omit<Contact, 'id'>) => void;
  canAddInvoice: boolean;
  canAddReceipt: boolean;
}

export const CustomersDetail: React.FC<CustomersDetailProps> = ({
  customer,
  customerDetail,
  activeTab,
  outstandingInvoices,
  customerMovements,
  loading,
  currentPage,
  totalPages,
  onTabChange,
  onPageChange,
  onInvoiceDoubleClick,
  onMovementDoubleClick,
  onAddInvoice,
  onAddReceipt,
  onSaveCustomer,
  onDianConsult,
  onAddContact,
  canAddInvoice,
  canAddReceipt,
}) => {
  return (
    <div className="card h-full">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">
            {customer ? customer.name : 'Seleccionar cliente de la lista'}
          </h3>
          {!customer && (
            <span className="text-sm text-gray-500">Sin datos para mostrar</span>
          )}
        </div>
      </div>

      <div className="card-content flex-1 overflow-hidden">
        <CustomerTabs
          customer={customer}
          customerDetail={customerDetail}
          activeTab={activeTab}
          outstandingInvoices={outstandingInvoices}
          customerMovements={customerMovements}
          loading={loading}
          currentPage={currentPage}
          totalPages={totalPages}
          onTabChange={onTabChange}
          onPageChange={onPageChange}
          onInvoiceDoubleClick={onInvoiceDoubleClick}
          onMovementDoubleClick={onMovementDoubleClick}
          onAddInvoice={onAddInvoice}
          onAddReceipt={onAddReceipt}
          onSaveCustomer={onSaveCustomer}
          onDianConsult={onDianConsult}
          onAddContact={onAddContact}
          canAddInvoice={canAddInvoice}
          canAddReceipt={canAddReceipt}
        />
      </div>
    </div>
  );
};