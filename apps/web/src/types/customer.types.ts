export interface Customer {
  id: string;
  code: string;
  name: string;
  tradeName?: string;
  nit?: string;
  documentType: DocumentType;
  address?: string;
  city?: string;
  state?: string;
  country: string;
  phone?: string;
  email?: string;
  taxResponsibility: TaxResponsibility;
  creditLimit: number;
  creditDays: number;
  active: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CustomerDetail extends Customer {
  // Datos comerciales adicionales
  saleCondition?: 'Contado' | 'Credito';
  discountPercentage?: number;
  ivaPercentage?: number;
  incomeAccount?: string;
  priceList?: string;
  transporter?: string;
  
  // Dirección de envío
  shippingAddress?: Address;
  
  // Contactos
  contacts?: Contact[];
}

export interface Address {
  address: string;
  city: string;
  postalCode: string;
  state: string;
  country: string;
}

export interface Contact {
  id: string;
  name: string;
  position: string;
  email: string;
  phone: string;
  notes?: string;
}

export interface OutstandingInvoice {
  id: string;
  number: string;
  type: string;
  dueDate?: string;
  description?: string;
  currencyValue: number;
  total: number;
  paidAmount: number;
  balance: number;
  accumulatedBalance: number;
}

export interface CustomerMovement {
  id: string;
  date: string;
  reference: string;
  type: string;
  description: string;
  currencyValue: number;
  debit: number;
  credit: number;
  balance: number;
}

export enum DocumentType {
  CC = 'CC',
  NIT = 'NIT',
  CE = 'CE',
  PASSPORT = 'PASSPORT'
}

export enum TaxResponsibility {
  RESPONSABLE_IVA = 'RESPONSABLE_IVA',
  NO_RESPONSABLE_IVA = 'NO_RESPONSABLE_IVA',
  GRAN_CONTRIBUYENTE = 'GRAN_CONTRIBUYENTE',
  REGIMEN_SIMPLE = 'REGIMEN_SIMPLE',
  PERSONA_NATURAL = 'PERSONA_NATURAL'
}

export interface CustomersState {
  customers: Customer[];
  filteredCustomers: Customer[];
  selectedCustomer: Customer | null;
  activeFilter: 'active' | 'inactive';
  searchTerm: string;
  currentPage: number;
  totalPages: number;
  pageSize: number;
  activeTab: string;
  isLoading: boolean;
  isInvoiceModalOpen: boolean;
  isContactModalOpen: boolean;
}

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  pageSize: number;
}