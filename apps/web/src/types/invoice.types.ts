export interface Invoice {
  id: string;
  number: string;
  customerId: string;
  date: string;
  dueDate?: string;
  saleCondition: 'Contado' | 'Credito';
  documentType: string;
  invoiceType: 'A' | 'B' | 'C';
  status: InvoiceStatus;
  description?: string;
  currency: string;
  priceList?: string;
  generateDelivery: boolean;
  items: InvoiceItem[];
  taxableNet: number;
  nonTaxableNet: number;
  totalIVA: number;
  ivaPerception: number;
  icaPerception: number;
  total: number;
  paidAmount: number;
  balance: number;
  attachments: Attachment[];
  createdAt: string;
  updatedAt: string;
}

export interface InvoiceItem {
  id: string;
  itemCode: string;
  description: string;
  warehouse: string;
  freight: number;
  salespeople: string[];
  unit: string;
  quantity: number;
  unitPrice: number;
  discountPercentage: number;
  ivaPercentage: number;
  subtotal: number;
  account: string;
}

export interface CreateInvoiceData {
  // Encabezado
  fechaFactura: Date;
  fechaVencimiento: Date;
  condicionVenta: 'Contado' | 'Credito';
  tipoComprobante: string;
  tipoFactura: 'A' | 'B' | 'C';
  estado: 'Borrador' | 'Aprobada';
  numeroFactura: string;
  descripcion: string;
  moneda: string;
  listaPrecio: string;
  generarRemito: boolean;
  
  // Items
  items: InvoiceItem[];
  
  // Totales (calculados)
  netoGravado: number;
  netoNoGravado: number;
  totalIVA: number;
  percepcionIVA: number;
  percepcionICA: number;
  totalFactura: number;
  
  // Adjuntos
  attachments: File[];
}

export interface Attachment {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
}

export enum InvoiceStatus {
  DRAFT = 'DRAFT',
  APPROVED = 'APPROVED',
  PAID = 'PAID',
  PARTIAL = 'PARTIAL',
  CANCELLED = 'CANCELLED'
}

export interface InvoiceTotals {
  netoGravado: number;
  netoNoGravado: number;
  totalIVA: number;
  percepcionIVA: number;
  percepcionICA: number;
  totalFactura: number;
}

export interface ValidationError {
  field: string;
  message: string;
}