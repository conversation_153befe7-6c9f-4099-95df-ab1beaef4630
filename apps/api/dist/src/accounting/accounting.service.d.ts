import { PrismaService } from '../prisma/prisma.service';
export declare class AccountingService {
    private prisma;
    constructor(prisma: PrismaService);
    getChartOfAccounts(): Promise<{
        id: string;
        name: string;
        active: boolean;
        createdAt: Date;
        updatedAt: Date;
        code: string;
        type: string;
        level: number;
        parent: string;
    }[]>;
    createJournalEntry(data: any): Promise<{
        lines: {
            id: string;
            createdAt: Date;
            debit: import("@prisma/client/runtime/library").Decimal;
            credit: import("@prisma/client/runtime/library").Decimal;
            accountId: string;
            entryId: string;
        }[];
    } & {
        number: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        description: string;
        date: Date;
        total: import("@prisma/client/runtime/library").Decimal;
        reference: string;
        posted: boolean;
    }>;
    getLedger(): Promise<({
        lines: ({
            account: {
                id: string;
                name: string;
                active: boolean;
                createdAt: Date;
                updatedAt: Date;
                code: string;
                type: string;
                level: number;
                parent: string;
            };
        } & {
            id: string;
            createdAt: Date;
            debit: import("@prisma/client/runtime/library").Decimal;
            credit: import("@prisma/client/runtime/library").Decimal;
            accountId: string;
            entryId: string;
        })[];
    } & {
        number: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        description: string;
        date: Date;
        total: import("@prisma/client/runtime/library").Decimal;
        reference: string;
        posted: boolean;
    })[]>;
    getTrialBalance(): Promise<any[]>;
}
