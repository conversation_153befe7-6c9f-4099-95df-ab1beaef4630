"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccountingController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const accounting_service_1 = require("./accounting.service");
let AccountingController = class AccountingController {
    constructor(accountingService) {
        this.accountingService = accountingService;
    }
    async getChartOfAccounts() {
        return this.accountingService.getChartOfAccounts();
    }
    async createJournalEntry(data) {
        return this.accountingService.createJournalEntry(data);
    }
    async getLedger() {
        return this.accountingService.getLedger();
    }
    async getTrialBalance() {
        return this.accountingService.getTrialBalance();
    }
};
__decorate([
    (0, common_1.Get)('chart-of-accounts'),
    (0, swagger_1.ApiOperation)({ summary: 'Obtener plan de cuentas' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AccountingController.prototype, "getChartOfAccounts", null);
__decorate([
    (0, common_1.Post)('journal-entries'),
    (0, swagger_1.ApiOperation)({ summary: 'Crear asiento contable' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AccountingController.prototype, "createJournalEntry", null);
__decorate([
    (0, common_1.Get)('ledger'),
    (0, swagger_1.ApiOperation)({ summary: 'Obtener libro mayor' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AccountingController.prototype, "getLedger", null);
__decorate([
    (0, common_1.Get)('trial-balance'),
    (0, swagger_1.ApiOperation)({ summary: 'Obtener balance de comprobación' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AccountingController.prototype, "getTrialBalance", null);
AccountingController = __decorate([
    (0, swagger_1.ApiTags)('Accounting'),
    (0, common_1.Controller)('api/accounting'),
    __metadata("design:paramtypes", [accounting_service_1.AccountingService])
], AccountingController);
exports.AccountingController = AccountingController;
//# sourceMappingURL=accounting.controller.js.map