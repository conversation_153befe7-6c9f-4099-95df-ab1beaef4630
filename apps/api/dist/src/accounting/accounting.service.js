"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccountingService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let AccountingService = class AccountingService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getChartOfAccounts() {
        return this.prisma.chartOfAccount.findMany({
            where: { active: true },
            orderBy: { code: 'asc' },
        });
    }
    async createJournalEntry(data) {
        const count = await this.prisma.journalEntry.count();
        const number = `ASI${(count + 1).toString().padStart(6, '0')}`;
        return this.prisma.journalEntry.create({
            data: {
                ...data,
                number,
                lines: {
                    create: data.lines,
                },
            },
            include: { lines: true },
        });
    }
    async getLedger() {
        return this.prisma.journalEntry.findMany({
            include: {
                lines: {
                    include: { account: true },
                },
            },
            orderBy: { date: 'desc' },
        });
    }
    async getTrialBalance() {
        return [];
    }
};
AccountingService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], AccountingService);
exports.AccountingService = AccountingService;
//# sourceMappingURL=accounting.service.js.map