import { SettingsService } from './settings.service';
export declare class SettingsController {
    private readonly settingsService;
    constructor(settingsService: SettingsService);
    getCompanySettings(): Promise<{
        id: string;
        email: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
        nit: string;
        tradeName: string;
        address: string;
        city: string;
        state: string;
        country: string;
        phone: string;
        website: string;
        logo: string;
        taxRegime: string;
        ivaResponsible: boolean;
        retentionAgent: boolean;
    }>;
    updateCompanySettings(data: any): Promise<{
        id: string;
        email: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
        nit: string;
        tradeName: string;
        address: string;
        city: string;
        state: string;
        country: string;
        phone: string;
        website: string;
        logo: string;
        taxRegime: string;
        ivaResponsible: boolean;
        retentionAgent: boolean;
    }>;
}
