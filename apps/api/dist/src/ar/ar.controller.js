"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ArController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const ar_service_1 = require("./ar.service");
let ArController = class ArController {
    constructor(arService) {
        this.arService = arService;
    }
    async getInvoices() {
        return this.arService.getInvoices();
    }
    async createInvoice(data) {
        return this.arService.createInvoice(data);
    }
    async createCredit(data) {
        return this.arService.createCredit(data);
    }
    async createReceipt(data) {
        return this.arService.createReceipt(data);
    }
};
__decorate([
    (0, common_1.Get)('invoices'),
    (0, swagger_1.ApiOperation)({ summary: 'Listar facturas de venta' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ArController.prototype, "getInvoices", null);
__decorate([
    (0, common_1.Post)('invoices'),
    (0, swagger_1.ApiOperation)({ summary: 'Crear factura de venta' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ArController.prototype, "createInvoice", null);
__decorate([
    (0, common_1.Post)('credits'),
    (0, swagger_1.ApiOperation)({ summary: 'Crear nota de crédito' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ArController.prototype, "createCredit", null);
__decorate([
    (0, common_1.Post)('receipts'),
    (0, swagger_1.ApiOperation)({ summary: 'Crear cobranza' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ArController.prototype, "createReceipt", null);
ArController = __decorate([
    (0, swagger_1.ApiTags)('Accounts Receivable'),
    (0, common_1.Controller)('api/ar'),
    __metadata("design:paramtypes", [ar_service_1.ArService])
], ArController);
exports.ArController = ArController;
//# sourceMappingURL=ar.controller.js.map