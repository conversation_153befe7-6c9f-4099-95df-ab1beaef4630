"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ArService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let ArService = class ArService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getInvoices() {
        return this.prisma.arInvoice.findMany({
            include: {
                customer: true,
                items: true,
            },
            orderBy: { date: 'desc' },
        });
    }
    async createInvoice(data) {
        const count = await this.prisma.arInvoice.count();
        const number = `FAC${(count + 1).toString().padStart(6, '0')}`;
        return this.prisma.arInvoice.create({
            data: {
                ...data,
                number,
                items: {
                    create: data.items,
                },
            },
            include: {
                customer: true,
                items: true,
            },
        });
    }
    async createCredit(data) {
        return {};
    }
    async createReceipt(data) {
        const count = await this.prisma.arReceipt.count();
        const number = `REC${(count + 1).toString().padStart(6, '0')}`;
        return this.prisma.arReceipt.create({
            data: {
                ...data,
                number,
                items: {
                    create: data.items,
                },
            },
            include: {
                customer: true,
                items: true,
            },
        });
    }
};
ArService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], ArService);
exports.ArService = ArService;
//# sourceMappingURL=ar.service.js.map