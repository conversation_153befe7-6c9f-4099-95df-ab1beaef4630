import { ArService } from './ar.service';
export declare class ArController {
    private readonly arService;
    constructor(arService: ArService);
    getInvoices(): Promise<({
        customer: {
            id: string;
            email: string;
            name: string;
            active: boolean;
            createdAt: Date;
            updatedAt: Date;
            nit: string;
            tradeName: string;
            address: string;
            city: string;
            state: string;
            country: string;
            phone: string;
            code: string;
            documentType: import(".prisma/client").$Enums.DocumentType;
            taxResponsibility: import(".prisma/client").$Enums.TaxResponsibility;
            creditLimit: import("@prisma/client/runtime/library").Decimal;
            creditDays: number;
        };
        items: {
            id: string;
            createdAt: Date;
            description: string;
            taxRate: import("@prisma/client/runtime/library").Decimal;
            taxAmount: import("@prisma/client/runtime/library").Decimal;
            total: import("@prisma/client/runtime/library").Decimal;
            quantity: import("@prisma/client/runtime/library").Decimal;
            unitPrice: import("@prisma/client/runtime/library").Decimal;
            itemId: string;
            invoiceId: string;
        }[];
    } & {
        number: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        prefix: string;
        customerId: string;
        date: Date;
        dueDate: Date;
        subtotal: import("@prisma/client/runtime/library").Decimal;
        taxAmount: import("@prisma/client/runtime/library").Decimal;
        total: import("@prisma/client/runtime/library").Decimal;
        status: import(".prisma/client").$Enums.InvoiceStatus;
        notes: string;
        currency: string;
        exchangeRate: import("@prisma/client/runtime/library").Decimal;
    })[]>;
    createInvoice(data: any): Promise<{
        customer: {
            id: string;
            email: string;
            name: string;
            active: boolean;
            createdAt: Date;
            updatedAt: Date;
            nit: string;
            tradeName: string;
            address: string;
            city: string;
            state: string;
            country: string;
            phone: string;
            code: string;
            documentType: import(".prisma/client").$Enums.DocumentType;
            taxResponsibility: import(".prisma/client").$Enums.TaxResponsibility;
            creditLimit: import("@prisma/client/runtime/library").Decimal;
            creditDays: number;
        };
        items: {
            id: string;
            createdAt: Date;
            description: string;
            taxRate: import("@prisma/client/runtime/library").Decimal;
            taxAmount: import("@prisma/client/runtime/library").Decimal;
            total: import("@prisma/client/runtime/library").Decimal;
            quantity: import("@prisma/client/runtime/library").Decimal;
            unitPrice: import("@prisma/client/runtime/library").Decimal;
            itemId: string;
            invoiceId: string;
        }[];
    } & {
        number: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        prefix: string;
        customerId: string;
        date: Date;
        dueDate: Date;
        subtotal: import("@prisma/client/runtime/library").Decimal;
        taxAmount: import("@prisma/client/runtime/library").Decimal;
        total: import("@prisma/client/runtime/library").Decimal;
        status: import(".prisma/client").$Enums.InvoiceStatus;
        notes: string;
        currency: string;
        exchangeRate: import("@prisma/client/runtime/library").Decimal;
    }>;
    createCredit(data: any): Promise<{}>;
    createReceipt(data: any): Promise<{
        customer: {
            id: string;
            email: string;
            name: string;
            active: boolean;
            createdAt: Date;
            updatedAt: Date;
            nit: string;
            tradeName: string;
            address: string;
            city: string;
            state: string;
            country: string;
            phone: string;
            code: string;
            documentType: import(".prisma/client").$Enums.DocumentType;
            taxResponsibility: import(".prisma/client").$Enums.TaxResponsibility;
            creditLimit: import("@prisma/client/runtime/library").Decimal;
            creditDays: number;
        };
        items: {
            id: string;
            createdAt: Date;
            invoiceId: string;
            amount: import("@prisma/client/runtime/library").Decimal;
            receiptId: string;
        }[];
    } & {
        number: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        customerId: string;
        date: Date;
        notes: string;
        amount: import("@prisma/client/runtime/library").Decimal;
    }>;
}
