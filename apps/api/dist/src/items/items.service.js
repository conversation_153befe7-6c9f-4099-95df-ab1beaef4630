"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ItemsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let ItemsService = class ItemsService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async findAll(search) {
        const where = {};
        if (search) {
            where.OR = [
                { name: { contains: search, mode: 'insensitive' } },
                { code: { contains: search, mode: 'insensitive' } },
            ];
        }
        return this.prisma.item.findMany({
            where,
            orderBy: { name: 'asc' },
        });
    }
    async findOne(id) {
        return this.prisma.item.findUnique({
            where: { id },
        });
    }
    async getMoves(id) {
        return this.prisma.stockMove.findMany({
            where: { itemId: id },
            include: { warehouse: true },
            orderBy: { date: 'desc' },
        });
    }
    async create(data) {
        const count = await this.prisma.item.count();
        const code = `ITM${(count + 1).toString().padStart(4, '0')}`;
        return this.prisma.item.create({
            data: { ...data, code },
        });
    }
    async update(id, data) {
        return this.prisma.item.update({
            where: { id },
            data,
        });
    }
};
ItemsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], ItemsService);
exports.ItemsService = ItemsService;
//# sourceMappingURL=items.service.js.map