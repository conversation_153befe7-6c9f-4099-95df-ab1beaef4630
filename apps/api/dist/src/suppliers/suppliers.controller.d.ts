import { SuppliersService } from './suppliers.service';
export declare class SuppliersController {
    private readonly suppliersService;
    constructor(suppliersService: SuppliersService);
    findAll(search?: string): Promise<{
        id: string;
        email: string;
        name: string;
        active: boolean;
        createdAt: Date;
        updatedAt: Date;
        nit: string;
        tradeName: string;
        address: string;
        city: string;
        state: string;
        country: string;
        phone: string;
        code: string;
        documentType: import(".prisma/client").$Enums.DocumentType;
        taxResponsibility: import(".prisma/client").$Enums.TaxResponsibility;
        bankAccount: string;
        bank: string;
    }[]>;
    findOne(id: string): Promise<{
        invoices: {
            number: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            date: Date;
            dueDate: Date;
            subtotal: import("@prisma/client/runtime/library").Decimal;
            taxAmount: import("@prisma/client/runtime/library").Decimal;
            total: import("@prisma/client/runtime/library").Decimal;
            status: import(".prisma/client").$Enums.InvoiceStatus;
            notes: string;
            currency: string;
            exchangeRate: import("@prisma/client/runtime/library").Decimal;
            retentions: import("@prisma/client/runtime/library").Decimal;
            supplierId: string;
        }[];
    } & {
        id: string;
        email: string;
        name: string;
        active: boolean;
        createdAt: Date;
        updatedAt: Date;
        nit: string;
        tradeName: string;
        address: string;
        city: string;
        state: string;
        country: string;
        phone: string;
        code: string;
        documentType: import(".prisma/client").$Enums.DocumentType;
        taxResponsibility: import(".prisma/client").$Enums.TaxResponsibility;
        bankAccount: string;
        bank: string;
    }>;
    create(createSupplierDto: any): Promise<{
        id: string;
        email: string;
        name: string;
        active: boolean;
        createdAt: Date;
        updatedAt: Date;
        nit: string;
        tradeName: string;
        address: string;
        city: string;
        state: string;
        country: string;
        phone: string;
        code: string;
        documentType: import(".prisma/client").$Enums.DocumentType;
        taxResponsibility: import(".prisma/client").$Enums.TaxResponsibility;
        bankAccount: string;
        bank: string;
    }>;
    update(id: string, updateSupplierDto: any): Promise<{
        id: string;
        email: string;
        name: string;
        active: boolean;
        createdAt: Date;
        updatedAt: Date;
        nit: string;
        tradeName: string;
        address: string;
        city: string;
        state: string;
        country: string;
        phone: string;
        code: string;
        documentType: import(".prisma/client").$Enums.DocumentType;
        taxResponsibility: import(".prisma/client").$Enums.TaxResponsibility;
        bankAccount: string;
        bank: string;
    }>;
}
