import { PrismaService } from '../prisma/prisma.service';
export declare class DashboardService {
    private prisma;
    constructor(prisma: PrismaService);
    getKpis(scope: string): Promise<{
        sales: {
            current: number | import("@prisma/client/runtime/library").Decimal;
            previous: number;
            change: number;
        };
        purchases: {
            current: number | import("@prisma/client/runtime/library").Decimal;
            previous: number;
            change: number;
        };
        accountsReceivable: {
            current: number | import("@prisma/client/runtime/library").Decimal;
            overdue: number;
        };
        accountsPayable: {
            current: number | import("@prisma/client/runtime/library").Decimal;
            overdue: number;
        };
    }>;
    getSeries(chart: string, scope: string): Promise<{
        series: any[];
    } | {
        series: any[];
        categories: any[];
    }>;
    private getIncomesExpensesSeries;
    private getCashSeries;
    private getInvoicesSeries;
    getExpenses(scope: string): Promise<{
        categories: string[];
        series: {
            name: string;
            data: number[];
        }[];
    }>;
    getDue(from?: string, to?: string, kind?: 'AR' | 'AP'): Promise<{
        id: string;
        type: string;
        number: string;
        customer: string;
        dueDate: Date;
        amount: import("@prisma/client/runtime/library").Decimal;
    }[] | {
        id: string;
        type: string;
        number: string;
        supplier: string;
        dueDate: Date;
        amount: import("@prisma/client/runtime/library").Decimal;
    }[]>;
}
