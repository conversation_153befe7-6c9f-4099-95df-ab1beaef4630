"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DashboardService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let DashboardService = class DashboardService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getKpis(scope) {
        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        const salesResult = await this.prisma.arInvoice.aggregate({
            where: {
                date: {
                    gte: startOfMonth,
                    lte: endOfMonth,
                },
                status: { not: 'CANCELLED' },
            },
            _sum: { total: true },
        });
        const purchasesResult = await this.prisma.apInvoice.aggregate({
            where: {
                date: {
                    gte: startOfMonth,
                    lte: endOfMonth,
                },
                status: { not: 'CANCELLED' },
            },
            _sum: { total: true },
        });
        const arResult = await this.prisma.arInvoice.aggregate({
            where: {
                status: { in: ['APPROVED', 'PARTIAL'] },
            },
            _sum: { total: true },
        });
        const apResult = await this.prisma.apInvoice.aggregate({
            where: {
                status: { in: ['APPROVED', 'PARTIAL'] },
            },
            _sum: { total: true },
        });
        return {
            sales: {
                current: salesResult._sum.total || 0,
                previous: 0,
                change: 0,
            },
            purchases: {
                current: purchasesResult._sum.total || 0,
                previous: 0,
                change: 0,
            },
            accountsReceivable: {
                current: arResult._sum.total || 0,
                overdue: 0,
            },
            accountsPayable: {
                current: apResult._sum.total || 0,
                overdue: 0,
            },
        };
    }
    async getSeries(chart, scope) {
        const now = new Date();
        const months = [];
        for (let i = 11; i >= 0; i--) {
            const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
            months.push({
                month: date.toISOString().substring(0, 7),
                name: date.toLocaleDateString('es-CO', { month: 'short' }),
            });
        }
        switch (chart) {
            case 'incomes_expenses':
                return this.getIncomesExpensesSeries(months);
            case 'cash':
                return this.getCashSeries(months);
            case 'invoices':
                return this.getInvoicesSeries(months);
            default:
                return { series: [], categories: [] };
        }
    }
    async getIncomesExpensesSeries(months) {
        const series = [];
        for (const month of months) {
            const startDate = new Date(month.month + '-01');
            const endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0);
            const incomes = await this.prisma.arInvoice.aggregate({
                where: {
                    date: { gte: startDate, lte: endDate },
                    status: { not: 'CANCELLED' },
                },
                _sum: { total: true },
            });
            const expenses = await this.prisma.apInvoice.aggregate({
                where: {
                    date: { gte: startDate, lte: endDate },
                    status: { not: 'CANCELLED' },
                },
                _sum: { total: true },
            });
            series.push({
                month: month.name,
                ingresos: incomes._sum.total || 0,
                gastos: expenses._sum.total || 0,
                saldo: Number(incomes._sum.total || 0) - Number(expenses._sum.total || 0),
            });
        }
        return { series };
    }
    async getCashSeries(months) {
        return { series: [] };
    }
    async getInvoicesSeries(months) {
        return { series: [] };
    }
    async getExpenses(scope) {
        return {
            categories: ['Costo de Mercadería'],
            series: [{ name: 'Gastos', data: [100] }],
        };
    }
    async getDue(from, to, kind) {
        const whereClause = {
            status: { in: ['APPROVED', 'PARTIAL'] },
        };
        if (from && to) {
            whereClause.dueDate = {
                gte: new Date(from),
                lte: new Date(to),
            };
        }
        if (kind === 'AR') {
            const invoices = await this.prisma.arInvoice.findMany({
                where: whereClause,
                include: { customer: true },
                orderBy: { dueDate: 'asc' },
            });
            return invoices.map(invoice => ({
                id: invoice.id,
                type: 'AR',
                number: invoice.number,
                customer: invoice.customer.name,
                dueDate: invoice.dueDate,
                amount: invoice.total,
            }));
        }
        else if (kind === 'AP') {
            const invoices = await this.prisma.apInvoice.findMany({
                where: whereClause,
                include: { supplier: true },
                orderBy: { dueDate: 'asc' },
            });
            return invoices.map(invoice => ({
                id: invoice.id,
                type: 'AP',
                number: invoice.number,
                supplier: invoice.supplier.name,
                dueDate: invoice.dueDate,
                amount: invoice.total,
            }));
        }
        return [];
    }
};
DashboardService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], DashboardService);
exports.DashboardService = DashboardService;
//# sourceMappingURL=dashboard.service.js.map