import { PrismaService } from '../prisma/prisma.service';
export declare class ApService {
    private prisma;
    constructor(prisma: PrismaService);
    getInvoices(): Promise<({
        supplier: {
            id: string;
            email: string;
            name: string;
            active: boolean;
            createdAt: Date;
            updatedAt: Date;
            nit: string;
            tradeName: string;
            address: string;
            city: string;
            state: string;
            country: string;
            phone: string;
            code: string;
            documentType: import(".prisma/client").$Enums.DocumentType;
            taxResponsibility: import(".prisma/client").$Enums.TaxResponsibility;
            bankAccount: string;
            bank: string;
        };
        items: {
            id: string;
            createdAt: Date;
            description: string;
            taxRate: import("@prisma/client/runtime/library").Decimal;
            taxAmount: import("@prisma/client/runtime/library").Decimal;
            total: import("@prisma/client/runtime/library").Decimal;
            quantity: import("@prisma/client/runtime/library").Decimal;
            unitPrice: import("@prisma/client/runtime/library").Decimal;
            itemId: string;
            invoiceId: string;
        }[];
    } & {
        number: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        date: Date;
        dueDate: Date;
        subtotal: import("@prisma/client/runtime/library").Decimal;
        taxAmount: import("@prisma/client/runtime/library").Decimal;
        total: import("@prisma/client/runtime/library").Decimal;
        status: import(".prisma/client").$Enums.InvoiceStatus;
        notes: string;
        currency: string;
        exchangeRate: import("@prisma/client/runtime/library").Decimal;
        retentions: import("@prisma/client/runtime/library").Decimal;
        supplierId: string;
    })[]>;
    createInvoice(data: any): Promise<{
        supplier: {
            id: string;
            email: string;
            name: string;
            active: boolean;
            createdAt: Date;
            updatedAt: Date;
            nit: string;
            tradeName: string;
            address: string;
            city: string;
            state: string;
            country: string;
            phone: string;
            code: string;
            documentType: import(".prisma/client").$Enums.DocumentType;
            taxResponsibility: import(".prisma/client").$Enums.TaxResponsibility;
            bankAccount: string;
            bank: string;
        };
        items: {
            id: string;
            createdAt: Date;
            description: string;
            taxRate: import("@prisma/client/runtime/library").Decimal;
            taxAmount: import("@prisma/client/runtime/library").Decimal;
            total: import("@prisma/client/runtime/library").Decimal;
            quantity: import("@prisma/client/runtime/library").Decimal;
            unitPrice: import("@prisma/client/runtime/library").Decimal;
            itemId: string;
            invoiceId: string;
        }[];
    } & {
        number: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        date: Date;
        dueDate: Date;
        subtotal: import("@prisma/client/runtime/library").Decimal;
        taxAmount: import("@prisma/client/runtime/library").Decimal;
        total: import("@prisma/client/runtime/library").Decimal;
        status: import(".prisma/client").$Enums.InvoiceStatus;
        notes: string;
        currency: string;
        exchangeRate: import("@prisma/client/runtime/library").Decimal;
        retentions: import("@prisma/client/runtime/library").Decimal;
        supplierId: string;
    }>;
}
