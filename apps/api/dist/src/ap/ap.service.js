"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let ApService = class ApService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getInvoices() {
        return this.prisma.apInvoice.findMany({
            include: {
                supplier: true,
                items: true,
            },
            orderBy: { date: 'desc' },
        });
    }
    async createInvoice(data) {
        return this.prisma.apInvoice.create({
            data: {
                ...data,
                items: {
                    create: data.items,
                },
            },
            include: {
                supplier: true,
                items: true,
            },
        });
    }
};
ApService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], ApService);
exports.ApService = ApService;
//# sourceMappingURL=ap.service.js.map