"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const dashboard_controller_1 = require("./dashboard/dashboard.controller");
const dashboard_service_1 = require("./dashboard/dashboard.service");
const prisma_service_1 = require("./prisma/prisma.service");
describe('DashboardController', () => {
    let controller;
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            controllers: [dashboard_controller_1.DashboardController],
            providers: [
                dashboard_service_1.DashboardService,
                {
                    provide: prisma_service_1.PrismaService,
                    useValue: {
                        arInvoice: {
                            aggregate: jest.fn().mockResolvedValue({ _sum: { total: 0 } }),
                        },
                        apInvoice: {
                            aggregate: jest.fn().mockResolvedValue({ _sum: { total: 0 } }),
                        },
                    },
                },
            ],
        }).compile();
        controller = module.get(dashboard_controller_1.DashboardController);
    });
    it('should be defined', () => {
        expect(controller).toBeDefined();
    });
    it('should return KPIs', async () => {
        const result = await controller.getKpis('monthly');
        expect(result).toBeDefined();
        expect(result.sales).toBeDefined();
        expect(result.purchases).toBeDefined();
    });
});
//# sourceMappingURL=app.controller.spec.js.map