import { TreasuryService } from './treasury.service';
export declare class TreasuryController {
    private readonly treasuryService;
    constructor(treasuryService: TreasuryService);
    getAccounts(): Promise<{
        id: string;
        name: string;
        active: boolean;
        createdAt: Date;
        updatedAt: Date;
        code: string;
        bank: string;
        type: import(".prisma/client").$Enums.TreasuryAccountType;
        accountNumber: string;
        balance: import("@prisma/client/runtime/library").Decimal;
    }[]>;
    getAccountLedger(id: string): Promise<{
        account: {
            id: string;
            name: string;
            active: boolean;
            createdAt: Date;
            updatedAt: Date;
            code: string;
            bank: string;
            type: import(".prisma/client").$Enums.TreasuryAccountType;
            accountNumber: string;
            balance: import("@prisma/client/runtime/library").Decimal;
        };
        transactions: {
            id: string;
            createdAt: Date;
            description: string;
            type: import(".prisma/client").$Enums.TreasuryTransactionType;
            date: Date;
            reference: string;
            amount: import("@prisma/client/runtime/library").Decimal;
            accountId: string;
        }[];
    }>;
    createAccount(data: any): Promise<{
        id: string;
        name: string;
        active: boolean;
        createdAt: Date;
        updatedAt: Date;
        code: string;
        bank: string;
        type: import(".prisma/client").$Enums.TreasuryAccountType;
        accountNumber: string;
        balance: import("@prisma/client/runtime/library").Decimal;
    }>;
    createApPayment(data: any): Promise<{
        number: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        date: Date;
        notes: string;
        retentions: import("@prisma/client/runtime/library").Decimal;
        amount: import("@prisma/client/runtime/library").Decimal;
        supplierId: string;
    }>;
    createArReceipt(data: any): Promise<{
        number: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        customerId: string;
        date: Date;
        notes: string;
        amount: import("@prisma/client/runtime/library").Decimal;
    }>;
}
