"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TreasuryService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let TreasuryService = class TreasuryService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getAccounts() {
        return this.prisma.treasuryAccount.findMany({
            where: { active: true },
            orderBy: { name: 'asc' },
        });
    }
    async getAccountLedger(id) {
        const account = await this.prisma.treasuryAccount.findUnique({
            where: { id },
        });
        const transactions = await this.prisma.treasuryTransaction.findMany({
            where: { accountId: id },
            orderBy: { date: 'desc' },
        });
        return { account, transactions };
    }
    async createAccount(data) {
        const count = await this.prisma.treasuryAccount.count();
        const code = `CTA${(count + 1).toString().padStart(3, '0')}`;
        return this.prisma.treasuryAccount.create({
            data: { ...data, code },
        });
    }
    async createApPayment(data) {
        return this.prisma.apPayment.create({
            data: {
                ...data,
                number: await this.generatePaymentNumber(),
            },
        });
    }
    async createArReceipt(data) {
        return this.prisma.arReceipt.create({
            data: {
                ...data,
                number: await this.generateReceiptNumber(),
            },
        });
    }
    async generatePaymentNumber() {
        const count = await this.prisma.apPayment.count();
        return `PAG${(count + 1).toString().padStart(6, '0')}`;
    }
    async generateReceiptNumber() {
        const count = await this.prisma.arReceipt.count();
        return `REC${(count + 1).toString().padStart(6, '0')}`;
    }
};
TreasuryService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], TreasuryService);
exports.TreasuryService = TreasuryService;
//# sourceMappingURL=treasury.service.js.map