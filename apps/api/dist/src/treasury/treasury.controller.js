"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TreasuryController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const treasury_service_1 = require("./treasury.service");
let TreasuryController = class TreasuryController {
    constructor(treasuryService) {
        this.treasuryService = treasuryService;
    }
    async getAccounts() {
        return this.treasuryService.getAccounts();
    }
    async getAccountLedger(id) {
        return this.treasuryService.getAccountLedger(id);
    }
    async createAccount(data) {
        return this.treasuryService.createAccount(data);
    }
    async createApPayment(data) {
        return this.treasuryService.createApPayment(data);
    }
    async createArReceipt(data) {
        return this.treasuryService.createArReceipt(data);
    }
};
__decorate([
    (0, common_1.Get)('accounts'),
    (0, swagger_1.ApiOperation)({ summary: 'Listar cuentas de tesorería' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TreasuryController.prototype, "getAccounts", null);
__decorate([
    (0, common_1.Get)('accounts/:id/ledger'),
    (0, swagger_1.ApiOperation)({ summary: 'Obtener movimientos de cuenta' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TreasuryController.prototype, "getAccountLedger", null);
__decorate([
    (0, common_1.Post)('accounts'),
    (0, swagger_1.ApiOperation)({ summary: 'Crear cuenta de tesorería' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], TreasuryController.prototype, "createAccount", null);
__decorate([
    (0, common_1.Post)('ap-payments'),
    (0, swagger_1.ApiOperation)({ summary: 'Registrar pago a proveedor' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], TreasuryController.prototype, "createApPayment", null);
__decorate([
    (0, common_1.Post)('ar-receipts'),
    (0, swagger_1.ApiOperation)({ summary: 'Registrar cobranza de cliente' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], TreasuryController.prototype, "createArReceipt", null);
TreasuryController = __decorate([
    (0, swagger_1.ApiTags)('Treasury'),
    (0, common_1.Controller)('api/treasury'),
    __metadata("design:paramtypes", [treasury_service_1.TreasuryService])
], TreasuryController);
exports.TreasuryController = TreasuryController;
//# sourceMappingURL=treasury.controller.js.map