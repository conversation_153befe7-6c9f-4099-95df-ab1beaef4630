import { InvoiceStatus } from '@prisma/client';
export declare class CreateArInvoiceItemDto {
    itemId?: string;
    description: string;
    quantity: number;
    unitPrice: number;
    taxRate: number;
    taxAmount: number;
    total: number;
}
export declare class CreateArInvoiceDto {
    customerId: string;
    number: string;
    prefix?: string;
    date: string;
    dueDate?: string;
    subtotal: number;
    taxAmount: number;
    total: number;
    status: InvoiceStatus;
    notes?: string;
    currency: string;
    exchangeRate: number;
    items: CreateArInvoiceItemDto[];
}
export declare class UpdateArInvoiceDto {
    number?: string;
    prefix?: string;
    date?: string;
    dueDate?: string;
    subtotal?: number;
    taxAmount?: number;
    total?: number;
    status?: InvoiceStatus;
    notes?: string;
    currency?: string;
    exchangeRate?: number;
}
