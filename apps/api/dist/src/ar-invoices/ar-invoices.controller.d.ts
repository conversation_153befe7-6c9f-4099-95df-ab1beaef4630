import { ArInvoicesService } from './ar-invoices.service';
import { CreateArInvoiceDto, UpdateArInvoiceDto } from './dto/ar-invoice.dto';
export declare class ArInvoicesController {
    private readonly arInvoicesService;
    constructor(arInvoicesService: ArInvoicesService);
    create(createArInvoiceDto: CreateArInvoiceDto): Promise<{
        customer: {
            id: string;
            email: string;
            name: string;
            active: boolean;
            createdAt: Date;
            updatedAt: Date;
            nit: string;
            tradeName: string;
            address: string;
            city: string;
            state: string;
            country: string;
            phone: string;
            code: string;
            documentType: import(".prisma/client").$Enums.DocumentType;
            taxResponsibility: import(".prisma/client").$Enums.TaxResponsibility;
            creditLimit: import("@prisma/client/runtime/library").Decimal;
            creditDays: number;
        };
        items: {
            id: string;
            createdAt: Date;
            description: string;
            taxRate: import("@prisma/client/runtime/library").Decimal;
            taxAmount: import("@prisma/client/runtime/library").Decimal;
            total: import("@prisma/client/runtime/library").Decimal;
            quantity: import("@prisma/client/runtime/library").Decimal;
            unitPrice: import("@prisma/client/runtime/library").Decimal;
            itemId: string;
            invoiceId: string;
        }[];
    } & {
        number: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        prefix: string;
        customerId: string;
        date: Date;
        dueDate: Date;
        subtotal: import("@prisma/client/runtime/library").Decimal;
        taxAmount: import("@prisma/client/runtime/library").Decimal;
        total: import("@prisma/client/runtime/library").Decimal;
        status: import(".prisma/client").$Enums.InvoiceStatus;
        notes: string;
        currency: string;
        exchangeRate: import("@prisma/client/runtime/library").Decimal;
    }>;
    findAll(customerId?: string, status?: string, page?: string, limit?: string): Promise<{
        data: ({
            customer: {
                id: string;
                email: string;
                name: string;
                active: boolean;
                createdAt: Date;
                updatedAt: Date;
                nit: string;
                tradeName: string;
                address: string;
                city: string;
                state: string;
                country: string;
                phone: string;
                code: string;
                documentType: import(".prisma/client").$Enums.DocumentType;
                taxResponsibility: import(".prisma/client").$Enums.TaxResponsibility;
                creditLimit: import("@prisma/client/runtime/library").Decimal;
                creditDays: number;
            };
            items: {
                id: string;
                createdAt: Date;
                description: string;
                taxRate: import("@prisma/client/runtime/library").Decimal;
                taxAmount: import("@prisma/client/runtime/library").Decimal;
                total: import("@prisma/client/runtime/library").Decimal;
                quantity: import("@prisma/client/runtime/library").Decimal;
                unitPrice: import("@prisma/client/runtime/library").Decimal;
                itemId: string;
                invoiceId: string;
            }[];
        } & {
            number: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            prefix: string;
            customerId: string;
            date: Date;
            dueDate: Date;
            subtotal: import("@prisma/client/runtime/library").Decimal;
            taxAmount: import("@prisma/client/runtime/library").Decimal;
            total: import("@prisma/client/runtime/library").Decimal;
            status: import(".prisma/client").$Enums.InvoiceStatus;
            notes: string;
            currency: string;
            exchangeRate: import("@prisma/client/runtime/library").Decimal;
        })[];
        pagination: {
            currentPage: number;
            totalPages: number;
            totalItems: number;
            pageSize: number;
        };
    }>;
    findOne(id: string): Promise<{
        customer: {
            id: string;
            email: string;
            name: string;
            active: boolean;
            createdAt: Date;
            updatedAt: Date;
            nit: string;
            tradeName: string;
            address: string;
            city: string;
            state: string;
            country: string;
            phone: string;
            code: string;
            documentType: import(".prisma/client").$Enums.DocumentType;
            taxResponsibility: import(".prisma/client").$Enums.TaxResponsibility;
            creditLimit: import("@prisma/client/runtime/library").Decimal;
            creditDays: number;
        };
        items: {
            id: string;
            createdAt: Date;
            description: string;
            taxRate: import("@prisma/client/runtime/library").Decimal;
            taxAmount: import("@prisma/client/runtime/library").Decimal;
            total: import("@prisma/client/runtime/library").Decimal;
            quantity: import("@prisma/client/runtime/library").Decimal;
            unitPrice: import("@prisma/client/runtime/library").Decimal;
            itemId: string;
            invoiceId: string;
        }[];
    } & {
        number: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        prefix: string;
        customerId: string;
        date: Date;
        dueDate: Date;
        subtotal: import("@prisma/client/runtime/library").Decimal;
        taxAmount: import("@prisma/client/runtime/library").Decimal;
        total: import("@prisma/client/runtime/library").Decimal;
        status: import(".prisma/client").$Enums.InvoiceStatus;
        notes: string;
        currency: string;
        exchangeRate: import("@prisma/client/runtime/library").Decimal;
    }>;
    update(id: string, updateArInvoiceDto: UpdateArInvoiceDto): Promise<{
        customer: {
            id: string;
            email: string;
            name: string;
            active: boolean;
            createdAt: Date;
            updatedAt: Date;
            nit: string;
            tradeName: string;
            address: string;
            city: string;
            state: string;
            country: string;
            phone: string;
            code: string;
            documentType: import(".prisma/client").$Enums.DocumentType;
            taxResponsibility: import(".prisma/client").$Enums.TaxResponsibility;
            creditLimit: import("@prisma/client/runtime/library").Decimal;
            creditDays: number;
        };
        items: {
            id: string;
            createdAt: Date;
            description: string;
            taxRate: import("@prisma/client/runtime/library").Decimal;
            taxAmount: import("@prisma/client/runtime/library").Decimal;
            total: import("@prisma/client/runtime/library").Decimal;
            quantity: import("@prisma/client/runtime/library").Decimal;
            unitPrice: import("@prisma/client/runtime/library").Decimal;
            itemId: string;
            invoiceId: string;
        }[];
    } & {
        number: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        prefix: string;
        customerId: string;
        date: Date;
        dueDate: Date;
        subtotal: import("@prisma/client/runtime/library").Decimal;
        taxAmount: import("@prisma/client/runtime/library").Decimal;
        total: import("@prisma/client/runtime/library").Decimal;
        status: import(".prisma/client").$Enums.InvoiceStatus;
        notes: string;
        currency: string;
        exchangeRate: import("@prisma/client/runtime/library").Decimal;
    }>;
    remove(id: string): Promise<{
        number: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        prefix: string;
        customerId: string;
        date: Date;
        dueDate: Date;
        subtotal: import("@prisma/client/runtime/library").Decimal;
        taxAmount: import("@prisma/client/runtime/library").Decimal;
        total: import("@prisma/client/runtime/library").Decimal;
        status: import(".prisma/client").$Enums.InvoiceStatus;
        notes: string;
        currency: string;
        exchangeRate: import("@prisma/client/runtime/library").Decimal;
    }>;
}
