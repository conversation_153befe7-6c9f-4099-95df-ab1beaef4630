"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ArInvoicesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let ArInvoicesService = class ArInvoicesService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createArInvoiceDto) {
        const { items, ...invoiceData } = createArInvoiceDto;
        if (!invoiceData.number) {
            const count = await this.prisma.arInvoice.count();
            invoiceData.number = `FAV-${(count + 1).toString().padStart(6, '0')}`;
        }
        const invoice = await this.prisma.arInvoice.create({
            data: {
                ...invoiceData,
                date: new Date(invoiceData.date),
                dueDate: invoiceData.dueDate ? new Date(invoiceData.dueDate) : null,
                items: {
                    create: items,
                },
            },
            include: {
                customer: true,
                items: true,
            },
        });
        await this.prisma.customerMovement.create({
            data: {
                customerId: invoice.customerId,
                type: 'INVOICE',
                reference: invoice.number,
                date: invoice.date,
                debit: invoice.total,
                credit: 0,
                balance: invoice.total,
            },
        });
        return invoice;
    }
    async findAll(customerId, status, page = 1, limit = 10) {
        const where = {};
        if (customerId) {
            where.customerId = customerId;
        }
        if (status) {
            if (status.includes(',')) {
                where.status = { in: status.split(',') };
            }
            else {
                where.status = status;
            }
        }
        const skip = (page - 1) * limit;
        const [invoices, total] = await Promise.all([
            this.prisma.arInvoice.findMany({
                where,
                include: {
                    customer: true,
                    items: true,
                },
                orderBy: { date: 'desc' },
                skip,
                take: limit,
            }),
            this.prisma.arInvoice.count({ where }),
        ]);
        return {
            data: invoices,
            pagination: {
                currentPage: page,
                totalPages: Math.ceil(total / limit),
                totalItems: total,
                pageSize: limit,
            },
        };
    }
    async findOne(id) {
        const invoice = await this.prisma.arInvoice.findUnique({
            where: { id },
            include: {
                customer: true,
                items: true,
            },
        });
        if (!invoice) {
            throw new common_1.NotFoundException('Factura no encontrada');
        }
        return invoice;
    }
    async update(id, updateArInvoiceDto) {
        await this.findOne(id);
        return this.prisma.arInvoice.update({
            where: { id },
            data: {
                ...updateArInvoiceDto,
                date: updateArInvoiceDto.date ? new Date(updateArInvoiceDto.date) : undefined,
                dueDate: updateArInvoiceDto.dueDate ? new Date(updateArInvoiceDto.dueDate) : undefined,
            },
            include: {
                customer: true,
                items: true,
            },
        });
    }
    async remove(id) {
        await this.findOne(id);
        await this.prisma.customerMovement.deleteMany({
            where: {
                type: 'INVOICE',
                reference: (await this.findOne(id)).number,
            },
        });
        return this.prisma.arInvoice.delete({
            where: { id },
        });
    }
};
ArInvoicesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], ArInvoicesService);
exports.ArInvoicesService = ArInvoicesService;
//# sourceMappingURL=ar-invoices.service.js.map