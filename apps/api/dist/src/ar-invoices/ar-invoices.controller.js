"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ArInvoicesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const ar_invoices_service_1 = require("./ar-invoices.service");
const ar_invoice_dto_1 = require("./dto/ar-invoice.dto");
let ArInvoicesController = class ArInvoicesController {
    constructor(arInvoicesService) {
        this.arInvoicesService = arInvoicesService;
    }
    async create(createArInvoiceDto) {
        return this.arInvoicesService.create(createArInvoiceDto);
    }
    async findAll(customerId, status, page, limit) {
        const pageNum = page ? parseInt(page, 10) : 1;
        const limitNum = limit ? parseInt(limit, 10) : 10;
        return this.arInvoicesService.findAll(customerId, status, pageNum, limitNum);
    }
    async findOne(id) {
        return this.arInvoicesService.findOne(id);
    }
    async update(id, updateArInvoiceDto) {
        return this.arInvoicesService.update(id, updateArInvoiceDto);
    }
    async remove(id) {
        return this.arInvoicesService.remove(id);
    }
};
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Crear factura de venta' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [ar_invoice_dto_1.CreateArInvoiceDto]),
    __metadata("design:returntype", Promise)
], ArInvoicesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Listar facturas de venta' }),
    __param(0, (0, common_1.Query)('customerId')),
    __param(1, (0, common_1.Query)('status')),
    __param(2, (0, common_1.Query)('page')),
    __param(3, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String]),
    __metadata("design:returntype", Promise)
], ArInvoicesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Obtener factura por ID' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ArInvoicesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Actualizar factura' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, ar_invoice_dto_1.UpdateArInvoiceDto]),
    __metadata("design:returntype", Promise)
], ArInvoicesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Eliminar factura' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ArInvoicesController.prototype, "remove", null);
ArInvoicesController = __decorate([
    (0, swagger_1.ApiTags)('AR Invoices'),
    (0, common_1.Controller)('api/ar/invoices'),
    __metadata("design:paramtypes", [ar_invoices_service_1.ArInvoicesService])
], ArInvoicesController);
exports.ArInvoicesController = ArInvoicesController;
//# sourceMappingURL=ar-invoices.controller.js.map