import { CustomersService } from './customers.service';
import { CreateCustomerDto, UpdateCustomerDto } from './dto/customer.dto';
export declare class CustomersController {
    private readonly customersService;
    constructor(customersService: CustomersService);
    findAll(search?: string, active?: boolean, page?: string, limit?: string): Promise<{
        data: {
            id: string;
            email: string;
            name: string;
            active: boolean;
            createdAt: Date;
            updatedAt: Date;
            nit: string;
            tradeName: string;
            address: string;
            city: string;
            state: string;
            country: string;
            phone: string;
            code: string;
            documentType: import(".prisma/client").$Enums.DocumentType;
            taxResponsibility: import(".prisma/client").$Enums.TaxResponsibility;
            creditLimit: import("@prisma/client/runtime/library").Decimal;
            creditDays: number;
        }[];
        pagination: {
            currentPage: number;
            totalPages: number;
            totalItems: number;
            pageSize: number;
        };
    }>;
    findOne(id: string): Promise<{
        invoices: {
            number: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            prefix: string;
            customerId: string;
            date: Date;
            dueDate: Date;
            subtotal: import("@prisma/client/runtime/library").Decimal;
            taxAmount: import("@prisma/client/runtime/library").Decimal;
            total: import("@prisma/client/runtime/library").Decimal;
            status: import(".prisma/client").$Enums.InvoiceStatus;
            notes: string;
            currency: string;
            exchangeRate: import("@prisma/client/runtime/library").Decimal;
        }[];
    } & {
        id: string;
        email: string;
        name: string;
        active: boolean;
        createdAt: Date;
        updatedAt: Date;
        nit: string;
        tradeName: string;
        address: string;
        city: string;
        state: string;
        country: string;
        phone: string;
        code: string;
        documentType: import(".prisma/client").$Enums.DocumentType;
        taxResponsibility: import(".prisma/client").$Enums.TaxResponsibility;
        creditLimit: import("@prisma/client/runtime/library").Decimal;
        creditDays: number;
    }>;
    getLedger(id: string, page?: string, limit?: string): Promise<{
        customer: {
            invoices: {
                number: string;
                id: string;
                createdAt: Date;
                updatedAt: Date;
                prefix: string;
                customerId: string;
                date: Date;
                dueDate: Date;
                subtotal: import("@prisma/client/runtime/library").Decimal;
                taxAmount: import("@prisma/client/runtime/library").Decimal;
                total: import("@prisma/client/runtime/library").Decimal;
                status: import(".prisma/client").$Enums.InvoiceStatus;
                notes: string;
                currency: string;
                exchangeRate: import("@prisma/client/runtime/library").Decimal;
            }[];
        } & {
            id: string;
            email: string;
            name: string;
            active: boolean;
            createdAt: Date;
            updatedAt: Date;
            nit: string;
            tradeName: string;
            address: string;
            city: string;
            state: string;
            country: string;
            phone: string;
            code: string;
            documentType: import(".prisma/client").$Enums.DocumentType;
            taxResponsibility: import(".prisma/client").$Enums.TaxResponsibility;
            creditLimit: import("@prisma/client/runtime/library").Decimal;
            creditDays: number;
        };
        movements: {
            id: string;
            createdAt: Date;
            type: string;
            balance: import("@prisma/client/runtime/library").Decimal;
            customerId: string;
            date: Date;
            reference: string;
            debit: import("@prisma/client/runtime/library").Decimal;
            credit: import("@prisma/client/runtime/library").Decimal;
        }[];
        balance: number | import("@prisma/client/runtime/library").Decimal;
        pagination: {
            currentPage: number;
            totalPages: number;
            totalItems: number;
            pageSize: number;
        };
    }>;
    getInvoices(id: string, status?: string, page?: string, limit?: string): Promise<{
        data: ({
            items: {
                id: string;
                createdAt: Date;
                description: string;
                taxRate: import("@prisma/client/runtime/library").Decimal;
                taxAmount: import("@prisma/client/runtime/library").Decimal;
                total: import("@prisma/client/runtime/library").Decimal;
                quantity: import("@prisma/client/runtime/library").Decimal;
                unitPrice: import("@prisma/client/runtime/library").Decimal;
                itemId: string;
                invoiceId: string;
            }[];
        } & {
            number: string;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            prefix: string;
            customerId: string;
            date: Date;
            dueDate: Date;
            subtotal: import("@prisma/client/runtime/library").Decimal;
            taxAmount: import("@prisma/client/runtime/library").Decimal;
            total: import("@prisma/client/runtime/library").Decimal;
            status: import(".prisma/client").$Enums.InvoiceStatus;
            notes: string;
            currency: string;
            exchangeRate: import("@prisma/client/runtime/library").Decimal;
        })[];
        pagination: {
            currentPage: number;
            totalPages: number;
            totalItems: number;
            pageSize: number;
        };
    }>;
    create(createCustomerDto: CreateCustomerDto): Promise<{
        id: string;
        email: string;
        name: string;
        active: boolean;
        createdAt: Date;
        updatedAt: Date;
        nit: string;
        tradeName: string;
        address: string;
        city: string;
        state: string;
        country: string;
        phone: string;
        code: string;
        documentType: import(".prisma/client").$Enums.DocumentType;
        taxResponsibility: import(".prisma/client").$Enums.TaxResponsibility;
        creditLimit: import("@prisma/client/runtime/library").Decimal;
        creditDays: number;
    }>;
    update(id: string, updateCustomerDto: UpdateCustomerDto): Promise<{
        id: string;
        email: string;
        name: string;
        active: boolean;
        createdAt: Date;
        updatedAt: Date;
        nit: string;
        tradeName: string;
        address: string;
        city: string;
        state: string;
        country: string;
        phone: string;
        code: string;
        documentType: import(".prisma/client").$Enums.DocumentType;
        taxResponsibility: import(".prisma/client").$Enums.TaxResponsibility;
        creditLimit: import("@prisma/client/runtime/library").Decimal;
        creditDays: number;
    }>;
    remove(id: string): Promise<{
        id: string;
        email: string;
        name: string;
        active: boolean;
        createdAt: Date;
        updatedAt: Date;
        nit: string;
        tradeName: string;
        address: string;
        city: string;
        state: string;
        country: string;
        phone: string;
        code: string;
        documentType: import(".prisma/client").$Enums.DocumentType;
        taxResponsibility: import(".prisma/client").$Enums.TaxResponsibility;
        creditLimit: import("@prisma/client/runtime/library").Decimal;
        creditDays: number;
    }>;
}
