import { DocumentType, TaxResponsibility } from '@prisma/client';
export declare class CreateCustomerDto {
    name: string;
    tradeName?: string;
    nit?: string;
    documentType: DocumentType;
    address?: string;
    city?: string;
    state?: string;
    country: string;
    phone?: string;
    email?: string;
    taxResponsibility: TaxResponsibility;
    creditLimit: number;
    creditDays: number;
    active: boolean;
}
export declare class UpdateCustomerDto {
    name?: string;
    tradeName?: string;
    nit?: string;
    documentType?: DocumentType;
    address?: string;
    city?: string;
    state?: string;
    country?: string;
    phone?: string;
    email?: string;
    taxResponsibility?: TaxResponsibility;
    creditLimit?: number;
    creditDays?: number;
    active?: boolean;
}
