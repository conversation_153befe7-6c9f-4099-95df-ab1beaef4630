"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomersService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let CustomersService = class CustomersService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async findAll(search, active, page = 1, limit = 20) {
        const where = {};
        if (search) {
            where.OR = [
                { name: { contains: search, mode: 'insensitive' } },
                { nit: { contains: search, mode: 'insensitive' } },
                { code: { contains: search, mode: 'insensitive' } },
            ];
        }
        if (active !== undefined) {
            where.active = active;
        }
        const skip = (page - 1) * limit;
        const [customers, total] = await Promise.all([
            this.prisma.customer.findMany({
                where,
                orderBy: { name: 'asc' },
                skip,
                take: limit,
            }),
            this.prisma.customer.count({ where }),
        ]);
        return {
            data: customers,
            pagination: {
                currentPage: page,
                totalPages: Math.ceil(total / limit),
                totalItems: total,
                pageSize: limit,
            },
        };
    }
    async findOne(id) {
        const customer = await this.prisma.customer.findUnique({
            where: { id },
            include: {
                invoices: {
                    orderBy: { date: 'desc' },
                    take: 10,
                },
            },
        });
        if (!customer) {
            throw new common_1.NotFoundException('Cliente no encontrado');
        }
        return customer;
    }
    async getLedger(id, page = 1, limit = 10) {
        const customer = await this.findOne(id);
        const skip = (page - 1) * limit;
        const [movements, total] = await Promise.all([
            this.prisma.customerMovement.findMany({
                where: { customerId: id },
                orderBy: { date: 'desc' },
                skip,
                take: limit,
            }),
            this.prisma.customerMovement.count({
                where: { customerId: id },
            }),
        ]);
        return {
            customer,
            movements,
            balance: movements.length > 0 ? movements[0].balance : 0,
            pagination: {
                currentPage: page,
                totalPages: Math.ceil(total / limit),
                totalItems: total,
                pageSize: limit,
            },
        };
    }
    async getInvoices(id, status, page = 1, limit = 10) {
        const where = { customerId: id };
        if (status) {
            if (status.includes(',')) {
                where.status = { in: status.split(',') };
            }
            else {
                where.status = status;
            }
        }
        const skip = (page - 1) * limit;
        const [invoices, total] = await Promise.all([
            this.prisma.arInvoice.findMany({
                where,
                include: {
                    items: true,
                },
                orderBy: { date: 'desc' },
                skip,
                take: limit,
            }),
            this.prisma.arInvoice.count({ where }),
        ]);
        return {
            data: invoices,
            pagination: {
                currentPage: page,
                totalPages: Math.ceil(total / limit),
                totalItems: total,
                pageSize: limit,
            },
        };
    }
    async create(createCustomerDto) {
        const count = await this.prisma.customer.count();
        const code = `CLI${(count + 1).toString().padStart(4, '0')}`;
        return this.prisma.customer.create({
            data: {
                ...createCustomerDto,
                code,
            },
        });
    }
    async update(id, updateCustomerDto) {
        await this.findOne(id);
        return this.prisma.customer.update({
            where: { id },
            data: updateCustomerDto,
        });
    }
    async remove(id) {
        await this.findOne(id);
        const invoiceCount = await this.prisma.arInvoice.count({
            where: { customerId: id },
        });
        if (invoiceCount > 0) {
            throw new Error('No se puede eliminar un cliente con facturas asociadas');
        }
        return this.prisma.customer.delete({
            where: { id },
        });
    }
};
CustomersService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], CustomersService);
exports.CustomersService = CustomersService;
//# sourceMappingURL=customers.service.js.map