"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const bcrypt = require("bcryptjs");
const prisma = new client_1.PrismaClient();
async function main() {
    console.log('🌱 Iniciando seed...');
    const hashedPassword = await bcrypt.hash('admin123', 10);
    const admin = await prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
            email: '<EMAIL>',
            password: hashedPassword,
            name: 'Administrador',
            role: 'ADMIN',
        },
    });
    const company = await prisma.companySettings.upsert({
        where: { nit: '*********-1' },
        update: {},
        create: {
            name: 'Empresa de Prueba S.A.S.',
            tradeName: 'Empresa de Prueba',
            nit: '*********-1',
            address: 'Carrera 7 #123-45',
            city: 'Bogotá',
            state: 'Cundinamarca',
            country: 'CO',
            phone: '+57 1 234 5678',
            email: '<EMAIL>',
        },
    });
    const customers = await Promise.all([
        prisma.customer.upsert({
            where: { code: 'CLI0001' },
            update: {},
            create: {
                code: 'CLI0001',
                name: '1 lumino',
                tradeName: '1 lumino',
                nit: '54000000',
                documentType: 'NIT',
                address: 'Calle 50 #25-30',
                city: 'Bogotá',
                state: 'Cundinamarca',
                phone: '+57 1 345 6789',
                email: '<EMAIL>',
                taxResponsibility: 'RESPONSABLE_IVA',
                creditLimit: 54000000,
                creditDays: 30,
            },
        }),
        prisma.customer.upsert({
            where: { code: 'CLI0002' },
            update: {},
            create: {
                code: 'CLI0002',
                name: '25 DE MAYO 743 S.R.L.',
                tradeName: '25 DE MAYO 743 S.R.L.',
                nit: '30695366-8',
                documentType: 'NIT',
                address: 'Avenida 68 #45-67',
                city: 'Mendoza',
                state: 'Mendoza',
                phone: '+57 4 567 8901',
                email: '<EMAIL>',
                taxResponsibility: 'RESPONSABLE_IVA',
                creditLimit: 142061.26,
                creditDays: 45,
            },
        }),
        prisma.customer.upsert({
            where: { code: 'CLI0003' },
            update: {},
            create: {
                code: 'CLI0003',
                name: '3A S.R.L.',
                tradeName: '3A S.R.L.',
                nit: '*********-3',
                documentType: 'NIT',
                address: 'Zona Industrial Calle 13',
                city: 'Cali',
                state: 'Valle del Cauca',
                phone: '+57 2 678 9012',
                email: '<EMAIL>',
                taxResponsibility: 'RESPONSABLE_IVA',
                creditLimit: 0,
                creditDays: 15,
            },
        }),
        prisma.customer.upsert({
            where: { code: 'CLI0004' },
            update: {},
            create: {
                code: 'CLI0004',
                name: 'A E B ARGENTINA SA',
                tradeName: 'A E B ARGENTINA SA',
                nit: '30695366-8',
                documentType: 'NIT',
                address: 'CARRIL RODRIGUEZ PEÑA 200-A 4094',
                city: 'COQUIMBO',
                state: 'Mendoza',
                phone: '+57 2 678 9012',
                email: '<EMAIL>',
                taxResponsibility: 'RESPONSABLE_IVA',
                creditLimit: 1649852.15,
                creditDays: 30,
            },
        }),
        prisma.customer.upsert({
            where: { code: 'CLI0005' },
            update: {},
            create: {
                code: 'CLI0005',
                name: 'A M G SRL',
                tradeName: 'A M G SRL',
                nit: '*********-5',
                documentType: 'NIT',
                address: 'Zona Industrial Norte',
                city: 'Rosario',
                state: 'Santa Fe',
                phone: '+54 ************',
                email: '<EMAIL>',
                taxResponsibility: 'RESPONSABLE_IVA',
                creditLimit: 27452.90,
                creditDays: 30,
            },
        }),
        prisma.customer.upsert({
            where: { code: 'CLI0006' },
            update: {},
            create: {
                code: 'CLI0006',
                name: 'AAR SHOP',
                tradeName: 'AAR SHOP',
                nit: '*********-6',
                documentType: 'NIT',
                address: 'Centro Comercial Plaza',
                city: 'Buenos Aires',
                state: 'Buenos Aires',
                phone: '+54 11 567 8901',
                email: '<EMAIL>',
                taxResponsibility: 'RESPONSABLE_IVA',
                creditLimit: 4005.16,
                creditDays: 15,
            },
        }),
        prisma.customer.upsert({
            where: { code: 'CLI0007' },
            update: {},
            create: {
                code: 'CLI0007',
                name: 'A.C.I.S. MAQUINAS & MET.',
                tradeName: 'TAWIL',
                nit: '*********-7',
                documentType: 'NIT',
                address: 'Parque Industrial Sur',
                city: 'Córdoba',
                state: 'Córdoba',
                phone: '+54 ************',
                email: '<EMAIL>',
                taxResponsibility: 'RESPONSABLE_IVA',
                creditLimit: 0,
                creditDays: 30,
            },
        }),
        prisma.customer.upsert({
            where: { code: 'CLI0008' },
            update: {},
            create: {
                code: 'CLI0008',
                name: 'A.Y.NOT DEAD SA',
                tradeName: 'A.Y.NOT DEAD SA',
                nit: '*********-8',
                documentType: 'NIT',
                address: 'Avenida Libertador 1234',
                city: 'La Plata',
                state: 'Buenos Aires',
                phone: '+54 ************',
                email: '<EMAIL>',
                taxResponsibility: 'RESPONSABLE_IVA',
                creditLimit: 1476295.25,
                creditDays: 45,
            },
        }),
    ]);
    const suppliers = await Promise.all([
        prisma.supplier.upsert({
            where: { code: 'PRO0001' },
            update: {},
            create: {
                code: 'PRO0001',
                name: 'LOPEZ DIESEL S.A.',
                nit: '*********-4',
                documentType: 'NIT',
                address: 'Autopista Norte Km 15',
                city: 'Bogotá',
                state: 'Cundinamarca',
                phone: '+57 1 789 0123',
                email: '<EMAIL>',
                taxResponsibility: 'RESPONSABLE_IVA',
                bankAccount: '*********',
                bank: 'Banco de Bogotá',
            },
        }),
        prisma.supplier.upsert({
            where: { code: 'PRO0002' },
            update: {},
            create: {
                code: 'PRO0002',
                name: 'Incauto S.A.',
                nit: '*********-5',
                documentType: 'NIT',
                address: 'Zona Franca Bogotá',
                city: 'Bogotá',
                state: 'Cundinamarca',
                phone: '+57 1 890 1234',
                email: '<EMAIL>',
                taxResponsibility: 'RESPONSABLE_IVA',
                bankAccount: '*********',
                bank: 'Bancolombia',
            },
        }),
        prisma.supplier.upsert({
            where: { code: 'PRO0003' },
            update: {},
            create: {
                code: 'PRO0003',
                name: 'TRANSPORTE LA MERIDIANA LTDA.',
                nit: '*********-6',
                documentType: 'NIT',
                address: 'Terminal de Transporte',
                city: 'Medellín',
                state: 'Antioquia',
                phone: '+57 4 901 2345',
                email: '<EMAIL>',
                taxResponsibility: 'RESPONSABLE_IVA',
                bankAccount: '*********',
                bank: 'Banco Popular',
            },
        }),
    ]);
    const warehouses = await Promise.all([
        prisma.warehouse.upsert({
            where: { code: 'DEP001' },
            update: {},
            create: {
                code: 'DEP001',
                name: 'Depósito Principal',
                address: 'Bodega 1 - Zona Industrial',
            },
        }),
        prisma.warehouse.upsert({
            where: { code: 'DEP002' },
            update: {},
            create: {
                code: 'DEP002',
                name: 'Depósito Secundario',
                address: 'Bodega 2 - Centro de Distribución',
            },
        }),
    ]);
    const items = await Promise.all([
        prisma.item.upsert({
            where: { code: 'ITM0001' },
            update: {},
            create: {
                code: 'ITM0001',
                name: 'AMOLADORA DE BANCO 125mm',
                description: 'Amoladora de banco 125mm profesional',
                type: 'PRODUCT',
                unit: 'UN',
                cost: 72077,
                price: 85000,
                taxRate: 19,
            },
        }),
        prisma.item.upsert({
            where: { code: 'ITM0002' },
            update: {},
            create: {
                code: 'ITM0002',
                name: 'ATORNILLADOR HEX 600W',
                description: 'Atornillador hexagonal 600W',
                type: 'PRODUCT',
                unit: 'UN',
                cost: 131402,
                price: 155000,
                taxRate: 19,
            },
        }),
        prisma.item.upsert({
            where: { code: 'ITM0003' },
            update: {},
            create: {
                code: 'ITM0003',
                name: 'MANGUERA AMARILLA FEMA 101',
                description: 'Manguera amarilla FEMA 101mm',
                type: 'PRODUCT',
                unit: 'MT',
                cost: 20017,
                price: 24000,
                taxRate: 19,
            },
        }),
        prisma.item.upsert({
            where: { code: 'ITM0004' },
            update: {},
            create: {
                code: 'ITM0004',
                name: 'MOTOBOMBA FEMA 2X2',
                description: 'Motobomba FEMA 2X2 pulgadas',
                type: 'PRODUCT',
                unit: 'UN',
                cost: 308354,
                price: 365000,
                taxRate: 19,
            },
        }),
        prisma.item.upsert({
            where: { code: 'ITM0005' },
            update: {},
            create: {
                code: 'ITM0005',
                name: 'Servicio de Instalación',
                description: 'Servicio técnico de instalación',
                type: 'SERVICE',
                unit: 'HR',
                cost: 0,
                price: 50000,
                taxRate: 19,
            },
        }),
    ]);
    const treasuryAccounts = await Promise.all([
        prisma.treasuryAccount.upsert({
            where: { code: 'CTA001' },
            update: {},
            create: {
                code: 'CTA001',
                name: 'CAJA DOLARESAC',
                type: 'CASH',
                balance: 0,
            },
        }),
        prisma.treasuryAccount.upsert({
            where: { code: 'CTA002' },
            update: {},
            create: {
                code: 'CTA002',
                name: 'Banco Supervielle',
                type: 'BANK',
                bank: 'Banco Supervielle',
                accountNumber: '**********',
                balance: 5677622.47,
            },
        }),
        prisma.treasuryAccount.upsert({
            where: { code: 'CTA003' },
            update: {},
            create: {
                code: 'CTA003',
                name: 'BANCO SANTANDER',
                type: 'BANK',
                bank: 'Banco Santander',
                accountNumber: '**********',
                balance: -371476.24,
            },
        }),
    ]);
    const chartAccounts = await Promise.all([
        prisma.chartOfAccount.upsert({
            where: { code: '1' },
            update: {},
            create: {
                code: '1',
                name: 'ACTIVO',
                type: 'ASSET',
                level: 1,
            },
        }),
        prisma.chartOfAccount.upsert({
            where: { code: '11' },
            update: {},
            create: {
                code: '11',
                name: 'ACTIVO CORRIENTE',
                type: 'ASSET',
                level: 2,
                parent: '1',
            },
        }),
        prisma.chartOfAccount.upsert({
            where: { code: '1105' },
            update: {},
            create: {
                code: '1105',
                name: 'CAJA',
                type: 'ASSET',
                level: 3,
                parent: '11',
            },
        }),
        prisma.chartOfAccount.upsert({
            where: { code: '1110' },
            update: {},
            create: {
                code: '1110',
                name: 'BANCOS',
                type: 'ASSET',
                level: 3,
                parent: '11',
            },
        }),
        prisma.chartOfAccount.upsert({
            where: { code: '1305' },
            update: {},
            create: {
                code: '1305',
                name: 'CLIENTES',
                type: 'ASSET',
                level: 3,
                parent: '11',
            },
        }),
        prisma.chartOfAccount.upsert({
            where: { code: '2' },
            update: {},
            create: {
                code: '2',
                name: 'PASIVO',
                type: 'LIABILITY',
                level: 1,
            },
        }),
        prisma.chartOfAccount.upsert({
            where: { code: '2205' },
            update: {},
            create: {
                code: '2205',
                name: 'PROVEEDORES',
                type: 'LIABILITY',
                level: 3,
                parent: '2',
            },
        }),
        prisma.chartOfAccount.upsert({
            where: { code: '4' },
            update: {},
            create: {
                code: '4',
                name: 'INGRESOS',
                type: 'INCOME',
                level: 1,
            },
        }),
        prisma.chartOfAccount.upsert({
            where: { code: '6' },
            update: {},
            create: {
                code: '6',
                name: 'GASTOS',
                type: 'EXPENSE',
                level: 1,
            },
        }),
    ]);
    const invoices = await Promise.all([
        prisma.arInvoice.upsert({
            where: { number: '0053-********' },
            update: {},
            create: {
                number: '0053-********',
                customerId: customers[1].id,
                date: new Date('2025-09-06'),
                dueDate: new Date('2025-09-06'),
                subtotal: 484000.00,
                taxAmount: 91960.00,
                total: 575960.36,
                status: 'APPROVED',
                currency: 'COP',
                items: {
                    create: [
                        {
                            itemId: items[4].id,
                            description: 'SERVICIO FLETE',
                            quantity: 1,
                            unitPrice: 484000.00,
                            taxRate: 19,
                            taxAmount: 91960.00,
                            total: 575960.36,
                        },
                    ],
                },
            },
        }),
        prisma.arInvoice.upsert({
            where: { number: '0053-00030259' },
            update: {},
            create: {
                number: '0053-00030259',
                customerId: customers[1].id,
                date: new Date('2025-11-07'),
                dueDate: new Date('2025-11-07'),
                subtotal: 256000.00,
                taxAmount: 48640.00,
                total: 304640.78,
                status: 'APPROVED',
                currency: 'COP',
                items: {
                    create: [
                        {
                            itemId: items[4].id,
                            description: 'SERVICIO FLETE',
                            quantity: 1,
                            unitPrice: 256000.00,
                            taxRate: 19,
                            taxAmount: 48640.00,
                            total: 304640.78,
                        },
                    ],
                },
            },
        }),
        prisma.arInvoice.upsert({
            where: { number: '0053-00030567' },
            update: {},
            create: {
                number: '0053-00030567',
                customerId: customers[1].id,
                date: new Date('2025-12-08'),
                dueDate: new Date('2025-12-08'),
                subtotal: 644000.00,
                taxAmount: 122360.00,
                total: 766360.01,
                status: 'APPROVED',
                currency: 'COP',
                items: {
                    create: [
                        {
                            itemId: items[4].id,
                            description: 'SERVICIO FLETE',
                            quantity: 1,
                            unitPrice: 644000.00,
                            taxRate: 19,
                            taxAmount: 122360.00,
                            total: 766360.01,
                        },
                    ],
                },
            },
        }),
        prisma.arInvoice.upsert({
            where: { number: '0053-00030400' },
            update: {},
            create: {
                number: '0053-00030400',
                customerId: customers[3].id,
                date: new Date('2025-09-06'),
                dueDate: new Date('2025-09-06'),
                subtotal: 484000.00,
                taxAmount: 91960.00,
                total: 575960.36,
                status: 'APPROVED',
                currency: 'COP',
                items: {
                    create: [
                        {
                            itemId: items[4].id,
                            description: 'SERVICIO FLETE',
                            quantity: 1,
                            unitPrice: 484000.00,
                            taxRate: 19,
                            taxAmount: 91960.00,
                            total: 575960.36,
                        },
                    ],
                },
            },
        }),
    ]);
    await prisma.customerMovement.deleteMany({});
    const customerMovements = await Promise.all([
        prisma.customerMovement.create({
            data: {
                customerId: customers[1].id,
                type: 'INVOICE',
                reference: '0053-********',
                date: new Date('2025-09-06'),
                debit: 575960.36,
                credit: 0,
                balance: 575960.36,
            },
        }),
        prisma.customerMovement.create({
            data: {
                customerId: customers[1].id,
                type: 'INVOICE',
                reference: '0053-00030259',
                date: new Date('2025-11-07'),
                debit: 304640.78,
                credit: 0,
                balance: 880601.14,
            },
        }),
        prisma.customerMovement.create({
            data: {
                customerId: customers[1].id,
                type: 'INVOICE',
                reference: '0053-00030567',
                date: new Date('2025-12-08'),
                debit: 766360.01,
                credit: 0,
                balance: 1646961.15,
            },
        }),
        prisma.customerMovement.create({
            data: {
                customerId: customers[3].id,
                type: 'INVOICE',
                reference: '0053-00030400',
                date: new Date('2025-09-06'),
                debit: 575960.36,
                credit: 0,
                balance: 575960.36,
            },
        }),
    ]);
    console.log('✅ Seed completado exitosamente');
    console.log(`👤 Usuario admin: <EMAIL> / admin123`);
    console.log(`🏢 Empresa: ${company.name}`);
    console.log(`👥 Clientes creados: ${customers.length}`);
    console.log(`🏭 Proveedores creados: ${suppliers.length}`);
    console.log(`📦 Items creados: ${items.length}`);
    console.log(`🏦 Cuentas de tesorería: ${treasuryAccounts.length}`);
    console.log(`📄 Facturas creadas: ${invoices.length}`);
    console.log(`📊 Movimientos de clientes: ${customerMovements.length}`);
}
main()
    .catch((e) => {
    console.error('❌ Error en seed:', e);
    process.exit(1);
})
    .finally(async () => {
    await prisma.$disconnect();
});
//# sourceMappingURL=seed.js.map