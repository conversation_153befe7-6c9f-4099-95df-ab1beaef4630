import { Controller, Get, Post, Put, Body, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { ItemsService } from './items.service';

@ApiTags('Items')
@Controller('api/items')
export class ItemsController {
  constructor(private readonly itemsService: ItemsService) {}

  @Get()
  @ApiOperation({ summary: 'Listar items' })
  async findAll(@Query('search') search?: string) {
    return this.itemsService.findAll(search);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Obtener item por ID' })
  async findOne(@Param('id') id: string) {
    return this.itemsService.findOne(id);
  }

  @Get(':id/moves')
  @ApiOperation({ summary: 'Obtener movimientos del item' })
  async getMoves(@Param('id') id: string) {
    return this.itemsService.getMoves(id);
  }

  @Post()
  @ApiOperation({ summary: 'Crear item' })
  async create(@Body() createItemDto: any) {
    return this.itemsService.create(createItemDto);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Actualizar item' })
  async update(@Param('id') id: string, @Body() updateItemDto: any) {
    return this.itemsService.update(id, updateItemDto);
  }
}