import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class ItemsService {
  constructor(private prisma: PrismaService) {}

  async findAll(search?: string) {
    const where: any = {};
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { code: { contains: search, mode: 'insensitive' } },
      ];
    }

    return this.prisma.item.findMany({
      where,
      orderBy: { name: 'asc' },
    });
  }

  async findOne(id: string) {
    return this.prisma.item.findUnique({
      where: { id },
    });
  }

  async getMoves(id: string) {
    return this.prisma.stockMove.findMany({
      where: { itemId: id },
      include: { warehouse: true },
      orderBy: { date: 'desc' },
    });
  }

  async create(data: any) {
    const count = await this.prisma.item.count();
    const code = `ITM${(count + 1).toString().padStart(4, '0')}`;
    
    return this.prisma.item.create({
      data: { ...data, code },
    });
  }

  async update(id: string, data: any) {
    return this.prisma.item.update({
      where: { id },
      data,
    });
  }
}