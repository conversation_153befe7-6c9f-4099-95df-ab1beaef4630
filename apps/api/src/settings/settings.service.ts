import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class SettingsService {
  constructor(private prisma: PrismaService) {}

  async getCompanySettings() {
    let settings = await this.prisma.companySettings.findFirst();
    
    if (!settings) {
      settings = await this.prisma.companySettings.create({
        data: {
          name: '<PERSON>presa de Prueba',
          nit: '*********-1',
          address: 'Calle 123 #45-67',
          city: 'Bogotá',
          state: 'Cundinamarca',
          country: 'CO',
        },
      });
    }

    return settings;
  }

  async updateCompanySettings(data: any) {
    const existing = await this.getCompanySettings();
    
    return this.prisma.companySettings.update({
      where: { id: existing.id },
      data,
    });
  }
}