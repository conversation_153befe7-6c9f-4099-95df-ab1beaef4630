import { Controller, Get, Put, Body } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { SettingsService } from './settings.service';

@ApiTags('Settings')
@Controller('api/settings')
export class SettingsController {
  constructor(private readonly settingsService: SettingsService) {}

  @Get('company')
  @ApiOperation({ summary: 'Obtener configuración de empresa' })
  async getCompanySettings() {
    return this.settingsService.getCompanySettings();
  }

  @Put('company')
  @ApiOperation({ summary: 'Actualizar configuración de empresa' })
  async updateCompanySettings(@Body() data: any) {
    return this.settingsService.updateCompanySettings(data);
  }
}