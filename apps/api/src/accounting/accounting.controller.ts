import { Controller, Get, Post, Body } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { AccountingService } from './accounting.service';

@ApiTags('Accounting')
@Controller('api/accounting')
export class AccountingController {
  constructor(private readonly accountingService: AccountingService) {}

  @Get('chart-of-accounts')
  @ApiOperation({ summary: 'Obtener plan de cuentas' })
  async getChartOfAccounts() {
    return this.accountingService.getChartOfAccounts();
  }

  @Post('journal-entries')
  @ApiOperation({ summary: 'Crear asiento contable' })
  async createJournalEntry(@Body() data: any) {
    return this.accountingService.createJournalEntry(data);
  }

  @Get('ledger')
  @ApiOperation({ summary: 'Obtener libro mayor' })
  async getLedger() {
    return this.accountingService.getLedger();
  }

  @Get('trial-balance')
  @ApiOperation({ summary: 'Obtener balance de comprobación' })
  async getTrialBalance() {
    return this.accountingService.getTrialBalance();
  }
}