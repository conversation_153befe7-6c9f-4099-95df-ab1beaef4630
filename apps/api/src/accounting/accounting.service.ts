import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class AccountingService {
  constructor(private prisma: PrismaService) {}

  async getChartOfAccounts() {
    return this.prisma.chartOfAccount.findMany({
      where: { active: true },
      orderBy: { code: 'asc' },
    });
  }

  async createJournalEntry(data: any) {
    const count = await this.prisma.journalEntry.count();
    const number = `ASI${(count + 1).toString().padStart(6, '0')}`;

    return this.prisma.journalEntry.create({
      data: {
        ...data,
        number,
        lines: {
          create: data.lines,
        },
      },
      include: { lines: true },
    });
  }

  async getLedger() {
    return this.prisma.journalEntry.findMany({
      include: {
        lines: {
          include: { account: true },
        },
      },
      orderBy: { date: 'desc' },
    });
  }

  async getTrialBalance() {
    // TODO: Implementar balance de comprobación
    return [];
  }
}