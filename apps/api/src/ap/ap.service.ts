import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class ApService {
  constructor(private prisma: PrismaService) {}

  async getInvoices() {
    return this.prisma.apInvoice.findMany({
      include: {
        supplier: true,
        items: true,
      },
      orderBy: { date: 'desc' },
    });
  }

  async createInvoice(data: any) {
    return this.prisma.apInvoice.create({
      data: {
        ...data,
        items: {
          create: data.items,
        },
      },
      include: {
        supplier: true,
        items: true,
      },
    });
  }
}