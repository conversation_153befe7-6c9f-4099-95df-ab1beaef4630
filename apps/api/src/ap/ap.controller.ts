import { Controller, Get, Post, Body } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { ApService } from './ap.service';

@ApiTags('Accounts Payable')
@Controller('api/ap')
export class ApController {
  constructor(private readonly apService: ApService) {}

  @Get('invoices')
  @ApiOperation({ summary: 'Listar facturas de compra' })
  async getInvoices() {
    return this.apService.getInvoices();
  }

  @Post('invoices')
  @ApiOperation({ summary: 'Crear factura de compra' })
  async createInvoice(@Body() data: any) {
    return this.apService.createInvoice(data);
  }
}