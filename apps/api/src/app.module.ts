import { Module } from '@nestjs/common';
import { PrismaModule } from './prisma/prisma.module';
import { AuthModule } from './auth/auth.module';
import { CustomersModule } from './customers/customers.module';
import { SuppliersModule } from './suppliers/suppliers.module';
import { ItemsModule } from './items/items.module';
import { TreasuryModule } from './treasury/treasury.module';
import { AccountingModule } from './accounting/accounting.module';
import { DashboardModule } from './dashboard/dashboard.module';
import { SettingsModule } from './settings/settings.module';
import { ArModule } from './ar/ar.module';
import { ApModule } from './ap/ap.module';

@Module({
  imports: [
    PrismaModule,
    AuthModule,
    CustomersModule,
    SuppliersModule,
    ItemsModule,
    TreasuryModule,
    AccountingModule,
    DashboardModule,
    SettingsModule,
    ArModule,
    ApModule,
  ],
})
export class AppModule {}