import { Controller, Get, Post, Put, Delete, Body, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { ArInvoicesService } from './ar-invoices.service';
import { CreateArInvoiceDto, UpdateArInvoiceDto } from './dto/ar-invoice.dto';

@ApiTags('AR Invoices')
@Controller('api/ar/invoices')
export class ArInvoicesController {
  constructor(private readonly arInvoicesService: ArInvoicesService) {}

  @Post()
  @ApiOperation({ summary: 'Crear factura de venta' })
  async create(@Body() createArInvoiceDto: CreateArInvoiceDto) {
    return this.arInvoicesService.create(createArInvoiceDto);
  }

  @Get()
  @ApiOperation({ summary: 'Listar facturas de venta' })
  async findAll(
    @Query('customerId') customerId?: string,
    @Query('status') status?: string,
    @Query('page') page?: string,
    @Query('limit') limit?: string
  ) {
    const pageNum = page ? parseInt(page, 10) : 1;
    const limitNum = limit ? parseInt(limit, 10) : 10;
    return this.arInvoicesService.findAll(customerId, status, pageNum, limitNum);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Obtener factura por ID' })
  async findOne(@Param('id') id: string) {
    return this.arInvoicesService.findOne(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Actualizar factura' })
  async update(@Param('id') id: string, @Body() updateArInvoiceDto: UpdateArInvoiceDto) {
    return this.arInvoicesService.update(id, updateArInvoiceDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Eliminar factura' })
  async remove(@Param('id') id: string) {
    return this.arInvoicesService.remove(id);
  }
}