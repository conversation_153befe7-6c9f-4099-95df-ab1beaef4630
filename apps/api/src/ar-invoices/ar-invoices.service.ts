import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateArInvoiceDto, UpdateArInvoiceDto } from './dto/ar-invoice.dto';

@Injectable()
export class ArInvoicesService {
  constructor(private prisma: PrismaService) {}

  async create(createArInvoiceDto: CreateArInvoiceDto) {
    const { items, ...invoiceData } = createArInvoiceDto;

    // Generar número automático si no se proporciona
    if (!invoiceData.number) {
      const count = await this.prisma.arInvoice.count();
      invoiceData.number = `FAV-${(count + 1).toString().padStart(6, '0')}`;
    }

    const invoice = await this.prisma.arInvoice.create({
      data: {
        ...invoiceData,
        date: new Date(invoiceData.date),
        dueDate: invoiceData.dueDate ? new Date(invoiceData.dueDate) : null,
        items: {
          create: items,
        },
      },
      include: {
        customer: true,
        items: true,
      },
    });

    // Crear movimiento de cliente
    await this.prisma.customerMovement.create({
      data: {
        customerId: invoice.customerId,
        type: 'INVOICE',
        reference: invoice.number,
        date: invoice.date,
        debit: invoice.total,
        credit: 0,
        balance: invoice.total, // Simplificado, debería calcular el balance real
      },
    });

    return invoice;
  }

  async findAll(customerId?: string, status?: string, page: number = 1, limit: number = 10) {
    const where: any = {};

    if (customerId) {
      where.customerId = customerId;
    }

    if (status) {
      if (status.includes(',')) {
        where.status = { in: status.split(',') };
      } else {
        where.status = status;
      }
    }

    const skip = (page - 1) * limit;

    const [invoices, total] = await Promise.all([
      this.prisma.arInvoice.findMany({
        where,
        include: {
          customer: true,
          items: true,
        },
        orderBy: { date: 'desc' },
        skip,
        take: limit,
      }),
      this.prisma.arInvoice.count({ where }),
    ]);

    return {
      data: invoices,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        pageSize: limit,
      },
    };
  }

  async findOne(id: string) {
    const invoice = await this.prisma.arInvoice.findUnique({
      where: { id },
      include: {
        customer: true,
        items: true,
      },
    });

    if (!invoice) {
      throw new NotFoundException('Factura no encontrada');
    }

    return invoice;
  }

  async update(id: string, updateArInvoiceDto: UpdateArInvoiceDto) {
    await this.findOne(id); // Verificar que existe

    return this.prisma.arInvoice.update({
      where: { id },
      data: {
        ...updateArInvoiceDto,
        date: updateArInvoiceDto.date ? new Date(updateArInvoiceDto.date) : undefined,
        dueDate: updateArInvoiceDto.dueDate ? new Date(updateArInvoiceDto.dueDate) : undefined,
      },
      include: {
        customer: true,
        items: true,
      },
    });
  }

  async remove(id: string) {
    await this.findOne(id); // Verificar que existe

    // Eliminar movimiento de cliente relacionado
    await this.prisma.customerMovement.deleteMany({
      where: {
        type: 'INVOICE',
        reference: (await this.findOne(id)).number,
      },
    });

    return this.prisma.arInvoice.delete({
      where: { id },
    });
  }
}