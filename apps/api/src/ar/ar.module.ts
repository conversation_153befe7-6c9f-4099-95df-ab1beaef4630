import { <PERSON>du<PERSON> } from '@nestjs/common';
import { Ar<PERSON>ontroller } from './ar.controller';
import { ArService } from './ar.service';
import { ArInvoicesController } from '../ar-invoices/ar-invoices.controller';
import { ArInvoicesService } from '../ar-invoices/ar-invoices.service';

@Module({
  controllers: [ArController, ArInvoicesController],
  providers: [ArService, ArInvoicesService],
  exports: [ArService, ArInvoicesService],
})
export class ArModule {}