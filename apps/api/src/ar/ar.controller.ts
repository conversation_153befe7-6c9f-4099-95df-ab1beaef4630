import { <PERSON>, Get, Post, Body, Param } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { ArService } from './ar.service';

@ApiTags('Accounts Receivable')
@Controller('api/ar')
export class ArController {
  constructor(private readonly arService: ArService) {}

  @Get('invoices')
  @ApiOperation({ summary: 'Listar facturas de venta' })
  async getInvoices() {
    return this.arService.getInvoices();
  }

  @Post('invoices')
  @ApiOperation({ summary: 'Crear factura de venta' })
  async createInvoice(@Body() data: any) {
    return this.arService.createInvoice(data);
  }

  @Post('credits')
  @ApiOperation({ summary: 'Crear nota de crédito' })
  async createCredit(@Body() data: any) {
    return this.arService.createCredit(data);
  }

  @Post('receipts')
  @ApiOperation({ summary: 'Crear cobranza' })
  async createReceipt(@Body() data: any) {
    return this.arService.createReceipt(data);
  }
}