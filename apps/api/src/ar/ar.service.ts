import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class ArService {
  constructor(private prisma: PrismaService) {}

  async getInvoices() {
    return this.prisma.arInvoice.findMany({
      include: {
        customer: true,
        items: true,
      },
      orderBy: { date: 'desc' },
    });
  }

  async createInvoice(data: any) {
    const count = await this.prisma.arInvoice.count();
    const number = `FAC${(count + 1).toString().padStart(6, '0')}`;

    return this.prisma.arInvoice.create({
      data: {
        ...data,
        number,
        items: {
          create: data.items,
        },
      },
      include: {
        customer: true,
        items: true,
      },
    });
  }

  async createCredit(data: any) {
    // TODO: Implementar nota de crédito
    return {};
  }

  async createReceipt(data: any) {
    const count = await this.prisma.arReceipt.count();
    const number = `REC${(count + 1).toString().padStart(6, '0')}`;

    return this.prisma.arReceipt.create({
      data: {
        ...data,
        number,
        items: {
          create: data.items,
        },
      },
      include: {
        customer: true,
        items: true,
      },
    });
  }
}