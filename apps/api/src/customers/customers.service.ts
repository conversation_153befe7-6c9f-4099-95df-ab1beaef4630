import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateCustomerDto, UpdateCustomerDto } from './dto/customer.dto';

@Injectable()
export class CustomersService {
  constructor(private prisma: PrismaService) {}

  async findAll(search?: string, active?: boolean, page: number = 1, limit: number = 20) {
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { nit: { contains: search, mode: 'insensitive' } },
        { code: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (active !== undefined) {
      where.active = active;
    }

    const skip = (page - 1) * limit;

    const [customers, total] = await Promise.all([
      this.prisma.customer.findMany({
        where,
        orderBy: { name: 'asc' },
        skip,
        take: limit,
      }),
      this.prisma.customer.count({ where }),
    ]);

    return {
      data: customers,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        pageSize: limit,
      },
    };
  }

  async findOne(id: string) {
    const customer = await this.prisma.customer.findUnique({
      where: { id },
      include: {
        invoices: {
          orderBy: { date: 'desc' },
          take: 10,
        },
      },
    });

    if (!customer) {
      throw new NotFoundException('Cliente no encontrado');
    }

    return customer;
  }

  async getLedger(id: string, page: number = 1, limit: number = 10) {
    const customer = await this.findOne(id);
    
    const skip = (page - 1) * limit;

    const [movements, total] = await Promise.all([
      this.prisma.customerMovement.findMany({
        where: { customerId: id },
        orderBy: { date: 'desc' },
        skip,
        take: limit,
      }),
      this.prisma.customerMovement.count({
        where: { customerId: id },
      }),
    ]);

    return {
      customer,
      movements,
      balance: movements.length > 0 ? movements[0].balance : 0,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        pageSize: limit,
      },
    };
  }

  async getInvoices(id: string, status?: string, page: number = 1, limit: number = 10) {
    const where: any = { customerId: id };
    
    if (status) {
      if (status.includes(',')) {
        where.status = { in: status.split(',') };
      } else {
        where.status = status;
      }
    }

    const skip = (page - 1) * limit;

    const [invoices, total] = await Promise.all([
      this.prisma.arInvoice.findMany({
        where,
        include: {
          items: true,
        },
        orderBy: { date: 'desc' },
        skip,
        take: limit,
      }),
      this.prisma.arInvoice.count({ where }),
    ]);

    return {
      data: invoices,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        pageSize: limit,
      },
    };
  }

  async create(createCustomerDto: CreateCustomerDto) {
    // Generar código automático
    const count = await this.prisma.customer.count();
    const code = `CLI${(count + 1).toString().padStart(4, '0')}`;

    return this.prisma.customer.create({
      data: {
        ...createCustomerDto,
        code,
      },
    });
  }

  async update(id: string, updateCustomerDto: UpdateCustomerDto) {
    await this.findOne(id); // Verificar que existe

    return this.prisma.customer.update({
      where: { id },
      data: updateCustomerDto,
    });
  }

  async remove(id: string) {
    await this.findOne(id); // Verificar que existe

    // Verificar que no tenga facturas
    const invoiceCount = await this.prisma.arInvoice.count({
      where: { customerId: id },
    });

    if (invoiceCount > 0) {
      throw new Error('No se puede eliminar un cliente con facturas asociadas');
    }

    return this.prisma.customer.delete({
      where: { id },
    });
  }
}