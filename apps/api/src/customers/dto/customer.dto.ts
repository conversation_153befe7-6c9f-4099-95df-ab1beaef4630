import { IsString, IsOptional, IsEmail, IsBoolean, IsEnum, IsDecimal } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { DocumentType, TaxResponsibility } from '@prisma/client';

export class CreateCustomerDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  tradeName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  nit?: string;

  @ApiProperty({ enum: DocumentType, default: DocumentType.NIT })
  @IsEnum(DocumentType)
  documentType: DocumentType;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  city?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  state?: string;

  @ApiProperty({ default: 'CO' })
  @IsString()
  country: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty({ enum: TaxResponsibility, default: TaxResponsibility.RESPONSABLE_IVA })
  @IsEnum(TaxResponsibility)
  taxResponsibility: TaxResponsibility;

  @ApiProperty({ default: 0 })
  @IsDecimal()
  creditLimit: number;

  @ApiProperty({ default: 0 })
  @IsDecimal()
  creditDays: number;

  @ApiProperty({ default: true })
  @IsBoolean()
  active: boolean;
}

export class UpdateCustomerDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  tradeName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  nit?: string;

  @ApiProperty({ enum: DocumentType, required: false })
  @IsOptional()
  @IsEnum(DocumentType)
  documentType?: DocumentType;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  city?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  state?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  country?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty({ enum: TaxResponsibility, required: false })
  @IsOptional()
  @IsEnum(TaxResponsibility)
  taxResponsibility?: TaxResponsibility;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDecimal()
  creditLimit?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDecimal()
  creditDays?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  active?: boolean;
}