import { Test, TestingModule } from '@nestjs/testing';
import { DashboardController } from './dashboard/dashboard.controller';
import { DashboardService } from './dashboard/dashboard.service';
import { PrismaService } from './prisma/prisma.service';

describe('DashboardController', () => {
  let controller: DashboardController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DashboardController],
      providers: [
        DashboardService,
        {
          provide: PrismaService,
          useValue: {
            arInvoice: {
              aggregate: jest.fn().mockResolvedValue({ _sum: { total: 0 } }),
            },
            apInvoice: {
              aggregate: jest.fn().mockResolvedValue({ _sum: { total: 0 } }),
            },
          },
        },
      ],
    }).compile();

    controller = module.get<DashboardController>(DashboardController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should return KPIs', async () => {
    const result = await controller.getKpis('monthly');
    expect(result).toBeDefined();
    expect(result.sales).toBeDefined();
    expect(result.purchases).toBeDefined();
  });
});