import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class DashboardService {
  constructor(private prisma: PrismaService) {}

  async getKpis(scope: string) {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    // Ventas del mes
    const salesResult = await this.prisma.arInvoice.aggregate({
      where: {
        date: {
          gte: startOfMonth,
          lte: endOfMonth,
        },
        status: { not: 'CANCELLED' },
      },
      _sum: { total: true },
    });

    // Compras del mes
    const purchasesResult = await this.prisma.apInvoice.aggregate({
      where: {
        date: {
          gte: startOfMonth,
          lte: endOfMonth,
        },
        status: { not: 'CANCELLED' },
      },
      _sum: { total: true },
    });

    // Por cobrar
    const arResult = await this.prisma.arInvoice.aggregate({
      where: {
        status: { in: ['APPROVED', 'PARTIAL'] },
      },
      _sum: { total: true },
    });

    // Por pagar
    const apResult = await this.prisma.apInvoice.aggregate({
      where: {
        status: { in: ['APPROVED', 'PARTIAL'] },
      },
      _sum: { total: true },
    });

    return {
      sales: {
        current: salesResult._sum.total || 0,
        previous: 0, // TODO: Calcular mes anterior
        change: 0,
      },
      purchases: {
        current: purchasesResult._sum.total || 0,
        previous: 0,
        change: 0,
      },
      accountsReceivable: {
        current: arResult._sum.total || 0,
        overdue: 0, // TODO: Calcular vencidas
      },
      accountsPayable: {
        current: apResult._sum.total || 0,
        overdue: 0,
      },
    };
  }

  async getSeries(chart: string, scope: string) {
    const now = new Date();
    const months = [];
    
    // Generar últimos 12 meses
    for (let i = 11; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      months.push({
        month: date.toISOString().substring(0, 7),
        name: date.toLocaleDateString('es-CO', { month: 'short' }),
      });
    }

    switch (chart) {
      case 'incomes_expenses':
        return this.getIncomesExpensesSeries(months);
      case 'cash':
        return this.getCashSeries(months);
      case 'invoices':
        return this.getInvoicesSeries(months);
      default:
        return { series: [], categories: [] };
    }
  }

  private async getIncomesExpensesSeries(months: any[]) {
    const series = [];
    
    for (const month of months) {
      const startDate = new Date(month.month + '-01');
      const endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0);

      const incomes = await this.prisma.arInvoice.aggregate({
        where: {
          date: { gte: startDate, lte: endDate },
          status: { not: 'CANCELLED' },
        },
        _sum: { total: true },
      });

      const expenses = await this.prisma.apInvoice.aggregate({
        where: {
          date: { gte: startDate, lte: endDate },
          status: { not: 'CANCELLED' },
        },
        _sum: { total: true },
      });

      series.push({
        month: month.name,
        ingresos: incomes._sum.total || 0,
        gastos: expenses._sum.total || 0,
        saldo: Number(incomes._sum.total || 0) - Number(expenses._sum.total || 0),
      });
    }

    return { series };
  }

  private async getCashSeries(months: any[]) {
    // TODO: Implementar serie de cajas y bancos
    return { series: [] };
  }

  private async getInvoicesSeries(months: any[]) {
    // TODO: Implementar serie de facturas
    return { series: [] };
  }

  async getExpenses(scope: string) {
    // TODO: Implementar distribución de gastos
    return {
      categories: ['Costo de Mercadería'],
      series: [{ name: 'Gastos', data: [100] }],
    };
  }

  async getDue(from?: string, to?: string, kind?: 'AR' | 'AP') {
    const whereClause: any = {
      status: { in: ['APPROVED', 'PARTIAL'] },
    };

    if (from && to) {
      whereClause.dueDate = {
        gte: new Date(from),
        lte: new Date(to),
      };
    }

    if (kind === 'AR') {
      const invoices = await this.prisma.arInvoice.findMany({
        where: whereClause,
        include: { customer: true },
        orderBy: { dueDate: 'asc' },
      });

      return invoices.map(invoice => ({
        id: invoice.id,
        type: 'AR',
        number: invoice.number,
        customer: invoice.customer.name,
        dueDate: invoice.dueDate,
        amount: invoice.total,
      }));
    } else if (kind === 'AP') {
      const invoices = await this.prisma.apInvoice.findMany({
        where: whereClause,
        include: { supplier: true },
        orderBy: { dueDate: 'asc' },
      });

      return invoices.map(invoice => ({
        id: invoice.id,
        type: 'AP',
        number: invoice.number,
        supplier: invoice.supplier.name,
        dueDate: invoice.dueDate,
        amount: invoice.total,
      }));
    }

    return [];
  }
}