import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { DashboardService } from './dashboard.service';

@ApiTags('Dashboard')
@Controller('api/dashboard')
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Get('kpis')
  @ApiOperation({ summary: 'Obtener KPIs del tablero' })
  async getKpis(@Query('scope') scope: string = 'monthly') {
    return this.dashboardService.getKpis(scope);
  }

  @Get('series')
  @ApiOperation({ summary: 'Obtener series de tiempo para gráficos' })
  async getSeries(
    @Query('chart') chart: string,
    @Query('scope') scope: string = 'monthly',
  ) {
    return this.dashboardService.getSeries(chart, scope);
  }

  @Get('expenses')
  @ApiOperation({ summary: 'Obtener distribución de gastos' })
  async getExpenses(@Query('scope') scope: string = 'weekly') {
    return this.dashboardService.getExpenses(scope);
  }

  @Get('due')
  @ApiOperation({ summary: 'Obtener calendario de vencimientos' })
  async getDue(
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('kind') kind?: 'AR' | 'AP',
  ) {
    return this.dashboardService.getDue(from, to, kind);
  }
}