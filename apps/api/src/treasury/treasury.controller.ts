import { Controller, Get, Post, Body, Param } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { TreasuryService } from './treasury.service';

@ApiTags('Treasury')
@Controller('api/treasury')
export class TreasuryController {
  constructor(private readonly treasuryService: TreasuryService) {}

  @Get('accounts')
  @ApiOperation({ summary: 'Listar cuentas de tesorería' })
  async getAccounts() {
    return this.treasuryService.getAccounts();
  }

  @Get('accounts/:id/ledger')
  @ApiOperation({ summary: 'Obtener movimientos de cuenta' })
  async getAccountLedger(@Param('id') id: string) {
    return this.treasuryService.getAccountLedger(id);
  }

  @Post('accounts')
  @ApiOperation({ summary: 'Crear cuenta de tesorería' })
  async createAccount(@Body() data: any) {
    return this.treasuryService.createAccount(data);
  }

  @Post('ap-payments')
  @ApiOperation({ summary: 'Registrar pago a proveedor' })
  async createApPayment(@Body() data: any) {
    return this.treasuryService.createApPayment(data);
  }

  @Post('ar-receipts')
  @ApiOperation({ summary: 'Registrar cobranza de cliente' })
  async createArReceipt(@Body() data: any) {
    return this.treasuryService.createArReceipt(data);
  }
}