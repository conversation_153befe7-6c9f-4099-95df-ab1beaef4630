import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class TreasuryService {
  constructor(private prisma: PrismaService) {}

  async getAccounts() {
    return this.prisma.treasuryAccount.findMany({
      where: { active: true },
      orderBy: { name: 'asc' },
    });
  }

  async getAccountLedger(id: string) {
    const account = await this.prisma.treasuryAccount.findUnique({
      where: { id },
    });

    const transactions = await this.prisma.treasuryTransaction.findMany({
      where: { accountId: id },
      orderBy: { date: 'desc' },
    });

    return { account, transactions };
  }

  async createAccount(data: any) {
    const count = await this.prisma.treasuryAccount.count();
    const code = `CTA${(count + 1).toString().padStart(3, '0')}`;
    
    return this.prisma.treasuryAccount.create({
      data: { ...data, code },
    });
  }

  async createApPayment(data: any) {
    // TODO: Implementar lógica de pago con retenciones
    return this.prisma.apPayment.create({
      data: {
        ...data,
        number: await this.generatePaymentNumber(),
      },
    });
  }

  async createArReceipt(data: any) {
    return this.prisma.arReceipt.create({
      data: {
        ...data,
        number: await this.generateReceiptNumber(),
      },
    });
  }

  private async generatePaymentNumber(): Promise<string> {
    const count = await this.prisma.apPayment.count();
    return `PAG${(count + 1).toString().padStart(6, '0')}`;
  }

  private async generateReceiptNumber(): Promise<string> {
    const count = await this.prisma.arReceipt.count();
    return `REC${(count + 1).toString().padStart(6, '0')}`;
  }
}