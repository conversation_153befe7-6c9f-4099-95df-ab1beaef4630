FROM node:18-alpine

# Instalar OpenSSL para Prisma
RUN apk add --no-cache openssl

WORKDIR /app

# Copiar package files
COPY package*.json ./
COPY prisma ./prisma/

# Instalar todas las dependencias (incluyendo dev para build)
RUN npm install

# Copiar código fuente
COPY . .

# Generar Prisma client
RUN npx prisma generate

# Build de la aplicación
RUN npm run build

# Limpiar dependencias de desarrollo
RUN npm prune --production

# Exponer puerto
EXPOSE 3001

# Comando de inicio
CMD ["sh", "-c", "sleep 10 && npx prisma db push && npm run start:prod"]