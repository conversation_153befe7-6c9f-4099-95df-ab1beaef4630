// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Usuarios y autenticación
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String
  role      UserRole @default(CONSULTA)
  active    Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("users")
}

enum UserRole {
  ADMIN
  CONTABILIDAD
  COMPRAS
  VENTAS
  TESORERIA
  CONSULTA
}

// Configuración de la empresa
model CompanySettings {
  id                String  @id @default(cuid())
  name              String
  tradeName         String?
  nit               String  @unique
  address           String
  city              String
  state             String
  country           String  @default("CO")
  phone             String?
  email             String?
  website           String?
  logo              String?
  taxRegime         String? // Régimen tributario
  ivaResponsible    Boolean @default(true)
  retentionAgent    Boolean @default(false)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@map("company_settings")
}

// Clientes
model Customer {
  id                String            @id @default(cuid())
  code              String            @unique
  name              String
  tradeName         String?
  nit               String?
  documentType      DocumentType      @default(NIT)
  address           String?
  city              String?
  state             String?
  country           String            @default("CO")
  phone             String?
  email             String?
  taxResponsibility TaxResponsibility @default(RESPONSABLE_IVA)
  creditLimit       Decimal           @default(0) @db.Decimal(15, 2)
  creditDays        Int               @default(0)
  active            Boolean           @default(true)
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relaciones
  invoices   ArInvoice[]
  receipts   ArReceipt[]
  movements  CustomerMovement[]

  @@map("customers")
}

// Proveedores
model Supplier {
  id                String            @id @default(cuid())
  code              String            @unique
  name              String
  tradeName         String?
  nit               String?
  documentType      DocumentType      @default(NIT)
  address           String?
  city              String?
  state             String?
  country           String            @default("CO")
  phone             String?
  email             String?
  taxResponsibility TaxResponsibility @default(RESPONSABLE_IVA)
  bankAccount       String?
  bank              String?
  active            Boolean           @default(true)
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relaciones
  invoices   ApInvoice[]
  payments   ApPayment[]
  movements  SupplierMovement[]

  @@map("suppliers")
}

enum DocumentType {
  CC
  NIT
  CE
  PASSPORT
}

enum TaxResponsibility {
  RESPONSABLE_IVA
  NO_RESPONSABLE_IVA
  GRAN_CONTRIBUYENTE
  REGIMEN_SIMPLE
  PERSONA_NATURAL
}

// Facturas de venta (AR)
model ArInvoice {
  id            String        @id @default(cuid())
  number        String        @unique
  prefix        String?
  customerId    String
  date          DateTime
  dueDate       DateTime?
  subtotal      Decimal       @db.Decimal(15, 2)
  taxAmount     Decimal       @db.Decimal(15, 2)
  total         Decimal       @db.Decimal(15, 2)
  status        InvoiceStatus @default(DRAFT)
  notes         String?
  currency      String        @default("COP")
  exchangeRate  Decimal       @default(1) @db.Decimal(10, 4)
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relaciones
  customer Customer         @relation(fields: [customerId], references: [id])
  items    ArInvoiceItem[]
  receipts ArReceiptItem[]

  @@map("ar_invoices")
}

model ArInvoiceItem {
  id          String    @id @default(cuid())
  invoiceId   String
  itemId      String?
  description String
  quantity    Decimal   @db.Decimal(15, 4)
  unitPrice   Decimal   @db.Decimal(15, 2)
  taxRate     Decimal   @default(0) @db.Decimal(5, 2)
  taxAmount   Decimal   @db.Decimal(15, 2)
  total       Decimal   @db.Decimal(15, 2)
  createdAt   DateTime  @default(now())

  // Relaciones
  invoice ArInvoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  item    Item?     @relation(fields: [itemId], references: [id])

  @@map("ar_invoice_items")
}

// Facturas de compra (AP)
model ApInvoice {
  id            String        @id @default(cuid())
  number        String
  supplierId    String
  date          DateTime
  dueDate       DateTime?
  subtotal      Decimal       @db.Decimal(15, 2)
  taxAmount     Decimal       @db.Decimal(15, 2)
  retentions    Decimal       @default(0) @db.Decimal(15, 2)
  total         Decimal       @db.Decimal(15, 2)
  status        InvoiceStatus @default(DRAFT)
  notes         String?
  currency      String        @default("COP")
  exchangeRate  Decimal       @default(1) @db.Decimal(10, 4)
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relaciones
  supplier Supplier         @relation(fields: [supplierId], references: [id])
  items    ApInvoiceItem[]
  payments ApPaymentItem[]

  @@map("ap_invoices")
}

model ApInvoiceItem {
  id          String    @id @default(cuid())
  invoiceId   String
  itemId      String?
  description String
  quantity    Decimal   @db.Decimal(15, 4)
  unitPrice   Decimal   @db.Decimal(15, 2)
  taxRate     Decimal   @default(0) @db.Decimal(5, 2)
  taxAmount   Decimal   @db.Decimal(15, 2)
  total       Decimal   @db.Decimal(15, 2)
  createdAt   DateTime  @default(now())

  // Relaciones
  invoice ApInvoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  item    Item?     @relation(fields: [itemId], references: [id])

  @@map("ap_invoice_items")
}

enum InvoiceStatus {
  DRAFT
  APPROVED
  PAID
  PARTIAL
  CANCELLED
}

// Cobranzas
model ArReceipt {
  id         String    @id @default(cuid())
  number     String    @unique
  customerId String
  date       DateTime
  amount     Decimal   @db.Decimal(15, 2)
  notes      String?
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt

  // Relaciones
  customer Customer       @relation(fields: [customerId], references: [id])
  items    ArReceiptItem[]

  @@map("ar_receipts")
}

model ArReceiptItem {
  id        String    @id @default(cuid())
  receiptId String
  invoiceId String
  amount    Decimal   @db.Decimal(15, 2)
  createdAt DateTime  @default(now())

  // Relaciones
  receipt ArReceipt @relation(fields: [receiptId], references: [id], onDelete: Cascade)
  invoice ArInvoice @relation(fields: [invoiceId], references: [id])

  @@map("ar_receipt_items")
}

// Pagos
model ApPayment {
  id         String    @id @default(cuid())
  number     String    @unique
  supplierId String
  date       DateTime
  amount     Decimal   @db.Decimal(15, 2)
  retentions Decimal   @default(0) @db.Decimal(15, 2)
  notes      String?
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt

  // Relaciones
  supplier Supplier        @relation(fields: [supplierId], references: [id])
  items    ApPaymentItem[]

  @@map("ap_payments")
}

model ApPaymentItem {
  id        String    @id @default(cuid())
  paymentId String
  invoiceId String
  amount    Decimal   @db.Decimal(15, 2)
  createdAt DateTime  @default(now())

  // Relaciones
  payment ApPayment @relation(fields: [paymentId], references: [id], onDelete: Cascade)
  invoice ApInvoice @relation(fields: [invoiceId], references: [id])

  @@map("ap_payment_items")
}

// Items/Productos
model Item {
  id          String   @id @default(cuid())
  code        String   @unique
  name        String
  description String?
  type        ItemType @default(PRODUCT)
  unit        String   @default("UN")
  cost        Decimal  @default(0) @db.Decimal(15, 2)
  price       Decimal  @default(0) @db.Decimal(15, 2)
  taxRate     Decimal  @default(19) @db.Decimal(5, 2)
  active      Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relaciones
  arItems     ArInvoiceItem[]
  apItems     ApInvoiceItem[]
  stockMoves  StockMove[]

  @@map("items")
}

enum ItemType {
  PRODUCT
  SERVICE
}

// Inventario
model Warehouse {
  id        String      @id @default(cuid())
  code      String      @unique
  name      String
  address   String?
  active    Boolean     @default(true)
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt

  // Relaciones
  stockMoves StockMove[]

  @@map("warehouses")
}

model StockMove {
  id          String        @id @default(cuid())
  itemId      String
  warehouseId String
  type        StockMoveType
  quantity    Decimal       @db.Decimal(15, 4)
  unitCost    Decimal       @db.Decimal(15, 2)
  totalCost   Decimal       @db.Decimal(15, 2)
  reference   String?
  date        DateTime
  createdAt   DateTime      @default(now())

  // Relaciones
  item      Item      @relation(fields: [itemId], references: [id])
  warehouse Warehouse @relation(fields: [warehouseId], references: [id])

  @@map("stock_moves")
}

enum StockMoveType {
  IN
  OUT
  TRANSFER
  ADJUSTMENT
}

// Tesorería
model TreasuryAccount {
  id           String              @id @default(cuid())
  code         String              @unique
  name         String
  type         TreasuryAccountType
  bank         String?
  accountNumber String?
  balance      Decimal             @default(0) @db.Decimal(15, 2)
  active       Boolean             @default(true)
  createdAt    DateTime            @default(now())
  updatedAt    DateTime            @updatedAt

  // Relaciones
  transactions TreasuryTransaction[]

  @@map("treasury_accounts")
}

enum TreasuryAccountType {
  CASH
  BANK
  CARD
}

model TreasuryTransaction {
  id          String                  @id @default(cuid())
  accountId   String
  type        TreasuryTransactionType
  amount      Decimal                 @db.Decimal(15, 2)
  description String
  reference   String?
  date        DateTime
  createdAt   DateTime                @default(now())

  // Relaciones
  account TreasuryAccount @relation(fields: [accountId], references: [id])

  @@map("treasury_transactions")
}

enum TreasuryTransactionType {
  DEPOSIT
  WITHDRAWAL
  TRANSFER
  PAYMENT
  RECEIPT
}

// Contabilidad
model ChartOfAccount {
  id       String  @id @default(cuid())
  code     String  @unique
  name     String
  type     String
  level    Int
  parent   String?
  active   Boolean @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relaciones
  journalEntries JournalEntryLine[]

  @@map("chart_of_accounts")
}

model JournalEntry {
  id          String    @id @default(cuid())
  number      String    @unique
  date        DateTime
  description String
  reference   String?
  total       Decimal   @db.Decimal(15, 2)
  posted      Boolean   @default(false)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relaciones
  lines JournalEntryLine[]

  @@map("journal_entries")
}

model JournalEntryLine {
  id        String    @id @default(cuid())
  entryId   String
  accountId String
  debit     Decimal   @default(0) @db.Decimal(15, 2)
  credit    Decimal   @default(0) @db.Decimal(15, 2)
  createdAt DateTime  @default(now())

  // Relaciones
  entry   JournalEntry    @relation(fields: [entryId], references: [id], onDelete: Cascade)
  account ChartOfAccount @relation(fields: [accountId], references: [id])

  @@map("journal_entry_lines")
}

// Movimientos de clientes y proveedores
model CustomerMovement {
  id         String    @id @default(cuid())
  customerId String
  type       String    // INVOICE, RECEIPT, CREDIT_NOTE
  reference  String
  date       DateTime
  debit      Decimal   @default(0) @db.Decimal(15, 2)
  credit     Decimal   @default(0) @db.Decimal(15, 2)
  balance    Decimal   @db.Decimal(15, 2)
  createdAt  DateTime  @default(now())

  // Relaciones
  customer Customer @relation(fields: [customerId], references: [id])

  @@map("customer_movements")
}

model SupplierMovement {
  id         String    @id @default(cuid())
  supplierId String
  type       String    // INVOICE, PAYMENT, CREDIT_NOTE
  reference  String
  date       DateTime
  debit      Decimal   @default(0) @db.Decimal(15, 2)
  credit     Decimal   @default(0) @db.Decimal(15, 2)
  balance    Decimal   @db.Decimal(15, 2)
  createdAt  DateTime  @default(now())

  // Relaciones
  supplier Supplier @relation(fields: [supplierId], references: [id])

  @@map("supplier_movements")
}