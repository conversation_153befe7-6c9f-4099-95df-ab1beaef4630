{"name": "finantic", "version": "1.0.0", "description": "ERP Contable para Colombia", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "concurrently \"npm run dev:api\" \"npm run dev:web\"", "dev:api": "cd apps/api && npm run start:dev", "dev:web": "cd apps/web && npm run dev", "build": "npm run build:api && npm run build:web", "build:api": "cd apps/api && npm run build", "build:web": "cd apps/web && npm run build", "test": "npm run test:api && npm run test:web", "test:api": "cd apps/api && npm run test", "test:web": "cd apps/web && npm run test", "lint": "npm run lint:api && npm run lint:web", "lint:api": "cd apps/api && npm run lint", "lint:web": "cd apps/web && npm run lint"}, "devDependencies": {"concurrently": "^8.2.2"}}