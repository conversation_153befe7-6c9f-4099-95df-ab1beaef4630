.PHONY: dev build test lint clean install

# Desarrollo
dev:
	docker-compose up --build

dev-local:
	npm install
	npm run dev

# Desarrollo con puerto personalizado
dev-port:
	@echo "Iniciando Finantic en puerto personalizado..."
	@read -p "Puerto para frontend (default 3002): " port; \
	port=$${port:-3002}; \
	sed -i.bak "s/3002:3002/$$port:$$port/g" docker-compose.yml; \
	sed -i.bak "s/PORT: 3002/PORT: $$port/g" docker-compose.yml; \
	docker-compose up --build; \
	mv docker-compose.yml.bak docker-compose.yml

# Build
build:
	docker-compose build

# Tests
test:
	npm run test

# Linting
lint:
	npm run lint

# Instalación
install:
	npm install
	cd apps/api && npm install
	cd apps/web && npm install

# Limpieza
clean:
	docker-compose down -v
	docker system prune -f

# Base de datos
db-reset:
	docker-compose down db
	docker volume rm finantic_postgres_data
	docker-compose up db -d

# Logs
logs:
	docker-compose logs -f

logs-api:
	docker-compose logs -f api

logs-web:
	docker-compose logs -f web

# Producción
prod:
	docker-compose -f docker-compose.prod.yml up --build -d